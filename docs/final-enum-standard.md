# 最终枚举标准化方案

## 设计原则

根据您的最终要求，采用最精简的枚举标准化设计：

1. **所有枚举值都写到value中，不使用序号**
2. **所有业务逻辑和存储逻辑均使用value值**
3. **根据接口文档的实际值类型确定存储类型**

## StandardEnum接口

```java
/**
 * 标准枚举接口
 *
 * 最精简设计：
 * 1. 所有枚举值都写到value中，不使用序号
 * 2. 所有业务逻辑和存储逻辑均使用value值
 * 3. 根据接口文档的实际值类型确定存储类型
 *
 * @param <T> 枚举值类型（根据API接口文档确定：Integer或String）
 */
public interface StandardEnum<T> {
    /**
     * 获取存储值
     * - 所有枚举值都写到value中，不使用序号
     * - 所有业务逻辑和存储逻辑均使用此值
     * - 直接对应API接口文档中的值
     */
    T getValue();

    /**
     * 获取描述信息
     */
    String getDescription();

    // 智能转换方法
    static <E extends Enum<E> & StandardEnum<?>> Optional<E> smartConvert(Class<E> enumClass, Object input);
    static <E extends Enum<E> & StandardEnum<?>> E smartConvertWithDefault(Class<E> enumClass, Object input, E defaultValue);
}
```

## 枚举实现示例

### 1. Integer类型枚举（InformationSensitivityType）

```java
/**
 * 敏感性分类枚举 - Integer存储
 * 
 * API字段: contentExt.sensitivityTypes
 * 存储方式: Integer存储 (API值)
 * API值: 1=敏感, 2=非敏感, 3=中性
 */
public enum InformationSensitivityType implements StandardEnum<Integer> {
    SENSITIVE(1, "敏感"),     // API值=1
    NON_SENSITIVE(2, "非敏感"), // API值=2  
    NEUTRAL(3, "中性");         // API值=3

    private final Integer value;
    private final String description;

    InformationSensitivityType(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value; // 直接返回API值，不使用序号
    }

    @Override
    public String getDescription() {
        return description;
    }
}
```

### 2. String类型枚举（SourceType）

```java
/**
 * 来源类型枚举 - 字符存储
 * 
 * API字段: contentExt.newOriginType
 * 存储方式: 字符存储 (API代码)
 * API值: hdlt, wb, wx, zmtapp, sp, szb, wz
 */
public enum SourceType implements StandardEnum<String> {
    INTERACTIVE_FORUMS("hdlt", "互动论坛"),
    WEIBO("wb", "微博"),
    WECHAT("wx", "微信"),
    MOBILE_APPS("zmtapp", "客户端"),
    VIDEO("sp", "视频"),
    DIGITAL_NEWSPAPERS("szb", "数字报"),
    WEBSITES("wz", "网站");

    private final String value;
    private final String description;

    SourceType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value; // 直接返回API代码，不使用序号
    }

    @Override
    public String getDescription() {
        return description;
    }
}
```

## 存储策略

### MySQL存储
- **Integer类型**：使用INTEGER存储getValue()返回的API值
- **String类型**：使用VARCHAR存储getValue()返回的API代码

```sql
CREATE TABLE content_analysis (
    id BIGINT PRIMARY KEY,
    sensitivity_type INTEGER NOT NULL COMMENT '敏感性类型: 1=敏感,2=非敏感,3=中性',
    source_type VARCHAR(10) NOT NULL COMMENT '来源类型: hdlt,wb,wx,zmtapp,sp,szb,wz',
    
    INDEX idx_sensitivity (sensitivity_type),
    INDEX idx_source (source_type)
);
```

### Elasticsearch映射
```json
{
  "mappings": {
    "properties": {
      "sensitivityType": {
        "type": "integer"
      },
      "sourceType": {
        "type": "keyword"
      }
    }
  }
}
```

## 使用示例

### 基本转换
```java
// Integer类型转换
InformationSensitivityType type1 = StandardEnum.smartConvertWithDefault(
    InformationSensitivityType.class, "敏感", InformationSensitivityType.NEUTRAL);

// String类型转换  
SourceType type2 = StandardEnum.smartConvertWithDefault(
    SourceType.class, "wb", SourceType.WEBSITES);

// API值转换
InformationSensitivityType type3 = StandardEnum.smartConvertWithDefault(
    InformationSensitivityType.class, 1, InformationSensitivityType.NEUTRAL); // 敏感
```

### 数据库操作
```java
// 存储 - 直接使用getValue()
entity.setSensitivityType(type.getValue()); // 存储API值：1, 2, 3
entity.setSourceType(sourceType.getValue()); // 存储API代码：hdlt, wb, wx等

// 查询 - 直接使用API值
List<Integer> sensitivityValues = Arrays.asList(1, 3); // 敏感和中性的API值
List<String> sourceValues = Arrays.asList("wb", "wx"); // 微博和微信的API代码
```

## 枚举类型清单

### Integer存储类型（直接使用API值）
1. **InformationSensitivityType** - 敏感性分类 (1,2,3)
2. **EmotionType** - 情绪类型 (需要根据API文档确定具体值)
3. **ContentCategory** - 内容类别 (1,2)
4. **ContentType** - 内容类型 (1,2,3,4)
5. **MatchType** - 匹配类型 (1,2)
6. **ResultViewType** - 结果呈现类型 (1,2)

### String存储类型（直接使用API代码）
1. **SourceType** - 来源类型 (hdlt,wb,wx,zmtapp,sp,szb,wz)
2. **DetailedSourceType** - 详细来源类型 (xw,zw,wbxw,wxzw,...)
3. **MediaLevel** - 媒体级别 (央级,省级,地市,重点,中小,企业商业)
4. **UserVerificationType** - 用户认证类型 (-1,0,1-7,200,220,600)

## 核心优势

1. **完全对应API**：枚举值直接对应API接口文档，无需转换
2. **存储高效**：直接存储API值，无额外映射开销
3. **查询简单**：查询条件直接使用API值，逻辑清晰
4. **维护简单**：枚举定义与API文档一一对应，易于维护
5. **类型安全**：编译时类型检查，避免运行时错误

## 迁移步骤

1. **更新枚举定义**：将所有枚举改为直接使用API值
2. **更新数据库**：修改存储字段以匹配API值类型
3. **更新业务逻辑**：所有地方都使用getValue()方法
4. **数据迁移**：将现有数据转换为新的API值格式
5. **测试验证**：确保所有功能正常工作

这个最终方案完全符合您的要求：所有枚举值都写到value中，不使用序号，所有业务逻辑和存储逻辑均使用value值，实现了最大的简洁性和与API的一致性。
