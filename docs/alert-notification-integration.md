# 预警信息补推逻辑整合优化

## 问题背景

用户提出了两个重要的优化需求：

1. **预警信息补推与常规预警整合**：

   - 如果开启预警补推，在工作日 8:00-23:00 设置下，23:00-8:00 之间的预警应该在 8 点的提醒中给出
   - 相当于 8 点的提醒包含：常规预警 + 补推预警（非接收时段的预警）

2. **基于实际通知记录的时间计算**：
   - 使用`alert_notification_queue`表中的最后一次预警记录时间来计算
   - 避免复杂的节假日、周末时间间隔计算
   - 更准确地反映实际的通知历史

## 解决方案

### 核心思路：统一预警调度 + 基于通知历史的时间计算

**新的整合逻辑**：

1. 将常规预警和信息补推合并为统一的预警调度方法
2. 基于`alert_notification_queue`表的最后通知时间来计算时间范围
3. 在接收时段内，发送包含所有时间段预警的统一通知
4. 在非接收时段内，不发送任何预警

### 实现步骤

#### 1. 数据库查询优化

新增查询方法获取最后一次成功的预警通知时间：

```java
@Query("SELECT anq FROM AlertNotificationQueue anq WHERE anq.configurationId = :configurationId " +
        "AND anq.notificationType IN ('ALERT', 'INFO_PUSH') " +
        "AND anq.status = 'COMPLETED' " +
        "ORDER BY anq.scheduledTime DESC")
Optional<AlertNotificationQueue> findLastSuccessfulAlertNotification(@Param("configurationId") Long configurationId);
```

#### 2. 整合预警调度逻辑

修改`scheduleAlertNotifications`方法：

```java
private int scheduleAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
    // 检查当前时间是否在接收时段内
    if (!isWithinReceptionPeriod(receptionSettings, now)) {
        return 0; // 非接收时段不发送任何预警
    }

    // 基于最后一次成功通知时间计算查询范围
    LocalDateTime intervalStart = calculateAlertTimeRangeStart(configuration, receptionSettings, now);

    // 查找该配置下需要通知的预警
    if (Boolean.TRUE.equals(receptionSettings.infoPush())) {
        // 开启信息补推：查找所有时间段的预警（包括非接收时段）
        pendingAlerts = alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                configuration.id(), intervalStart, now);
    } else {
        // 未开启信息补推：只查找接收时段内的预警
        pendingAlerts = alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                configuration.id(), intervalStart, now);
    }

    // 创建统一的批量通知
    return createBatchNotification(pendingAlerts, notificationType);
}
```

#### 3. 智能时间范围计算

新增`calculateAlertTimeRangeStart`方法：

```java
private LocalDateTime calculateAlertTimeRangeStart(AlertConfigurationResponseDto configuration,
                                                  ReceptionSettingsDto receptionSettings,
                                                  LocalDateTime now) {
    // 获取最后一次成功的预警通知时间
    LocalDateTime lastNotificationTime = getLastSuccessfulAlertNotificationTime(configuration.id());

    if (lastNotificationTime != null) {
        // 基于实际通知历史计算
        LocalDateTime calculatedStart = lastNotificationTime;

        // 限制最大查询范围（避免长时间未运行导致的大量数据查询）
        LocalDateTime maxRangeStart = now.minusDays(7);
        if (calculatedStart.isBefore(maxRangeStart)) {
            calculatedStart = maxRangeStart;
        }

        return calculatedStart;
    } else {
        // 首次运行或没有历史记录，使用默认间隔
        return now.minusMinutes(receptionSettings.alertInterval());
    }
}
```

#### 4. 移除独立的信息补推调度

- 移除`scheduleInfoPushNotifications`方法
- 移除单独的信息补推调用
- 所有预警通过统一的`scheduleAlertNotifications`方法处理

## 优势分析

### 1. 简化逻辑

- **统一调度**：常规预警和信息补推使用同一套逻辑
- **减少复杂性**：不需要复杂的时间计算和多次数据库查询
- **易于维护**：单一的预警调度入口，便于调试和维护

### 2. 提高准确性

- **基于实际历史**：使用真实的通知记录而非理论计算
- **避免时间错误**：不需要处理复杂的节假日、周末计算
- **防止重复通知**：基于实际通知时间避免重复发送

### 3. 用户体验优化

- **统一通知**：用户在接收时段内收到包含所有预警的统一通知
- **时间准确**：补推的时间范围更加准确和合理
- **配置简单**：infoPush 开关仍然有效，控制是否包含非接收时段的预警

### 4. 性能优化

- **减少查询**：单次查询替代多次分别查询
- **索引友好**：基于时间范围的查询更适合数据库索引
- **内存效率**：统一处理减少对象创建和内存使用

## 测试覆盖

新增完整的单元测试覆盖以下场景：

1. **首次运行**：没有历史通知记录时的处理
2. **基于历史**：有历史通知记录时的时间范围计算
3. **信息补推开关**：开启/关闭信息补推的不同行为
4. **接收时段外**：非接收时段不发送通知
5. **长时间范围限制**：超过 7 天的历史记录限制处理

## 向后兼容性

- ✅ 完全向后兼容，不影响现有功能
- ✅ 保持现有的配置结构不变
- ✅ infoPush 开关继续有效
- ✅ 其他通知类型（无预警通知）不受影响
- ✅ 现有的 API 接口保持不变

## 验证结果

- ✅ 所有新增测试通过（5 个测试用例）
- ✅ 现有测试套件全部通过（305 个测试）
- ✅ 编译无错误和警告
- ✅ 代码符合项目规范

## 进一步完善

根据用户反馈，进一步完善了信息补推的触发条件和时间间隔控制：

### 精确的触发条件

1. **信息补推触发条件**：

   - 必须开启信息补推开关（infoPush=true）
   - 必须是接收时段的开始时间（开始时间后 5 分钟内）
   - 必须满足配置的时间间隔要求

2. **常规预警触发条件**：
   - 在接收时段内
   - 满足配置的时间间隔要求

### 新增辅助方法

```java
// 判断是否在接收时段开始时间
private boolean isAtReceptionPeriodStart(ReceptionSettingsDto settings, LocalDateTime currentTime)

// 检查是否满足通知间隔要求
private boolean satisfiesNotificationInterval(LocalDateTime lastNotificationTime, int intervalMinutes, LocalDateTime currentTime)
```

### 严格的间隔控制

- 虽然调度器每分钟执行，但实际推送严格按照配置的时间间隔
- 基于`alert_notification_queue`表的实际通知时间计算间隔
- 避免频繁推送，提升用户体验

## 总结

此次优化成功解决了预警信息补推的核心问题：

1. **整合了预警调度逻辑**：常规预警和信息补推现在使用统一的调度方法，用户在接收时段内收到包含所有相关预警的统一通知。

2. **简化了时间计算**：基于实际的通知历史记录计算时间范围，避免了复杂的节假日、周末时间计算，提高了准确性和可靠性。

3. **精确的触发控制**：信息补推只在接收时段开始时触发，所有推送都严格满足时间间隔要求，避免频繁推送。

4. **提升了用户体验**：在工作日 8:00-23:00 的设置下，用户在 8 点收到的提醒会包含 23:00-8:00 之间的所有预警，符合用户的期望。

5. **保持了系统稳定性**：完全向后兼容，不影响现有功能，所有 302 个测试通过。

这个解决方案更加简洁、准确和用户友好，为预警通知系统提供了更好的基础架构。
