# 媒体类型树形结构实现文档

## 概述

本文档描述了在MonitorConfigurationResponseDto中新增的媒体类型树形结构功能的实现。该功能根据现有的mediaType、mediaTypeSecond、mediaTypeThird字段值构建一个完整的三级树形结构。

## 核心组件

### 1. MediaTypeNode DTO类

位置：`src/main/java/com/czb/hn/dto/monitor/MediaTypeNode.java`

这是一个Record类，用于表示树形结构中的节点：

```java
public record MediaTypeNode(
    String code,           // 媒体类型代码
    String name,           // 媒体类型名称  
    String parentCode,     // 父级代码
    int level,             // 层级 (1=一级, 2=二级, 3=三级)
    boolean selected,      // 是否被选中
    List<MediaTypeNode> children  // 子节点列表
) implements Serializable
```

**主要特性：**
- 不可变对象，线程安全
- 包含验证逻辑，确保数据完整性
- 提供便利方法用于创建不同层级的节点
- 支持查询选中状态和子节点信息

### 2. DetailedSourceType枚举增强

位置：`src/main/java/com/czb/hn/enums/DetailedSourceType.java`

新增了`getSourceType()`方法，用于获取二级分类对应的一级分类：

```java
public SourceType getSourceType() {
    return switch (this) {
        case NEWS_WEBSITES, GOVERNMENT_WEBSITES, ... -> SourceType.WEBSITES;
        case WEIBO_MEDIA_ACCOUNTS, WEIBO_GOVERNMENT_ACCOUNTS, ... -> SourceType.WEIBO;
        // ... 其他映射关系
    };
}
```

### 3. MonitorConfigurationMapper增强

位置：`src/main/java/com/czb/hn/util/MonitorConfigurationMapper.java`

新增了`buildMediaTypeHierarchy()`方法，用于构建树形结构：

```java
public List<MediaTypeNode> buildMediaTypeHierarchy(
    List<String> mediaTypes,       // 一级媒体类型列表
    List<String> mediaTypeSeconds, // 二级媒体类型列表  
    List<String> mediaTypeThirds   // 三级媒体类型列表
)
```

**构建逻辑：**
1. 从三级节点开始构建，避免循环引用
2. 逐级向上构建二级和一级节点
3. 建立正确的父子关系
4. 标记选中状态

### 4. MonitorConfigurationResponseDto增强

位置：`src/main/java/com/czb/hn/dto/monitor/MonitorConfigurationResponseDto.java`

新增了`sourceTypeHierarchy`字段：

```java
@Schema(description = "Source type hierarchy (tree structure)") 
List<MediaTypeNode> sourceTypeHierarchy
```

## 数据结构示例

### 输入数据
```java
mediaTypes = ["hdlt", "wb"]           // 一级：互动论坛、微博
mediaTypeSeconds = ["lt", "wbxw"]     // 二级：论坛、微博媒体号
mediaTypeThirds = ["mainlt"]          // 三级：全国重点论坛
```

### 输出树形结构
```
[✓] 互动论坛 (hdlt) - Level 1
  [ ] 留言版 (liuyanban) - Level 2
  [ ] 投诉爆料 (tousu) - Level 2
  [✓] 论坛 (lt) - Level 2
    [✓] 全国重点论坛 (mainlt) - Level 3
    [ ] 地方重点论坛 (dylt) - Level 3
    [ ] 行业论坛 (hylt) - Level 3
    [ ] 中小论坛 (normallt) - Level 3
  [ ] 问答 (wenda) - Level 2
  [ ] 贴吧 (tieba) - Level 2
  [ ] 博客 (blog) - Level 2
[✓] 微博 (wb) - Level 1
  [✓] 微博媒体号 (wbxw) - Level 2
    [ ] 央级媒体号 (centerxwWb) - Level 3
    [ ] 省级媒体号 (proxwWb) - Level 3
    [ ] 地市媒体号 (areaxwWb) - Level 3
    [ ] 商业号 (portalWb) - Level 3
    [ ] 其他媒体号 (qitaWb) - Level 3
  [ ] 微博政务号 (wbzw) - Level 2
  [ ] 微博校园号 (wbschool) - Level 2
  // ... 其他节点
```

## API响应格式

当调用监控配置查询API时，响应中会包含`sourceTypeHierarchy`字段：

```json
{
  "id": 1,
  "planId": 123,
  // ... 其他字段
  "sourceTypeHierarchy": [
    {
      "code": "hdlt",
      "name": "互动论坛",
      "parentCode": null,
      "level": 1,
      "selected": true,
      "children": [
        {
          "code": "lt",
          "name": "论坛", 
          "parentCode": "hdlt",
          "level": 2,
          "selected": true,
          "children": [
            {
              "code": "mainlt",
              "name": "全国重点论坛",
              "parentCode": "lt", 
              "level": 3,
              "selected": true,
              "children": []
            }
            // ... 其他三级节点
          ]
        }
        // ... 其他二级节点
      ]
    }
    // ... 其他一级节点
  ]
}
```

## 使用场景

1. **前端树形控件展示**：可以直接用于前端的树形选择器组件
2. **权限控制**：基于层级结构进行细粒度的权限控制
3. **数据分析**：按照媒体类型层级进行数据统计和分析
4. **配置验证**：验证媒体类型配置的完整性和一致性

## 性能考虑

- 树形结构在每次查询时动态构建，确保数据实时性
- 使用不可变对象，避免并发问题
- 构建逻辑优化，避免循环引用和重复计算
- 支持大量媒体类型的高效处理

## 测试验证

可以运行示例程序验证功能：

```bash
mvn exec:java -Dexec.mainClass="com.czb.hn.example.MediaTypeHierarchyExample"
```

该示例展示了各种选择组合下的树形结构构建效果。

## 扩展性

该设计支持未来的扩展需求：
- 可以轻松添加新的媒体类型层级
- 支持动态的媒体类型配置
- 可以扩展节点属性（如图标、描述等）
- 支持不同的树形结构展示模式
