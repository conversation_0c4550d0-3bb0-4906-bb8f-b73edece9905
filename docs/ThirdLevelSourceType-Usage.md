# ThirdLevelSourceType 枚举使用说明

## 概述

`ThirdLevelSourceType` 是基于 `DetailedSourceType` 的进一步细分，提供了更精确的三级来源分类。该枚举包含41个具体的来源类型，覆盖了从论坛、问答平台到政府网站等各种媒体来源。

## 枚举结构

### 1. 论坛类 (论坛 lt)
- `NATIONAL_KEY_FORUMS` - 全国重点论坛 (mainlt)
- `LOCAL_KEY_FORUMS` - 地方重点论坛 (dylt)
- `INDUSTRY_FORUMS` - 行业论坛 (hylt)
- `SMALL_MEDIUM_FORUMS` - 中小论坛 (normallt)

### 2. 问答类 (问答 wenda)
- `KEY_QA_PLATFORMS` - 重点问答 (mainwd)
- `OTHER_QA_PLATFORMS` - 其他问答 (normalwd)

### 3. 微博媒体号类 (微博媒体号 wbxw)
- `WEIBO_CENTRAL_MEDIA` - 央级媒体号 (centerxwWb)
- `WEIBO_PROVINCIAL_MEDIA` - 省级媒体号 (proxwWb)
- `WEIBO_MUNICIPAL_MEDIA` - 地市媒体号 (areaxwWb)
- `WEIBO_COMMERCIAL_MEDIA` - 商业号 (portalWb)
- `WEIBO_OTHER_MEDIA` - 其他媒体号 (qitaWb)

### 4. 微博政务号类 (微博政务号 wbzw)
- `WEIBO_CENTRAL_GOVERNMENT` - 央级政府号 (centerDepWb)
- `WEIBO_PROVINCIAL_GOVERNMENT` - 省级政府号 (proDepWb)
- `WEIBO_MUNICIPAL_GOVERNMENT` - 市级政府号 (cityDepWb)
- `WEIBO_DISTRICT_GOVERNMENT` - 区县政府号 (areaDepWb)
- `WEIBO_OVERSEAS_INSTITUTIONS` - 驻境外机构号 (jwDepWb)

### 5. 微信媒体号类 (微信媒体号 wxxw)
- `WECHAT_CENTRAL_MEDIA` - 央级媒体公众号 (centerxwWx)
- `WECHAT_PROVINCIAL_MEDIA` - 省级媒体公众号 (proxwWx)
- `WECHAT_MUNICIPAL_MEDIA` - 地市媒体公众号 (areaxwWx)
- `WECHAT_COMMERCIAL_MEDIA` - 商业媒体公众号 (portalWx)

### 6. 微信政务号类 (微信政务号 wxzw)
- `WECHAT_CENTRAL_GOVERNMENT` - 央级政府公众号 (centerDepWx)
- `WECHAT_PROVINCIAL_GOVERNMENT` - 省级政府公众号 (proDepWx)
- `WECHAT_MUNICIPAL_GOVERNMENT` - 市级政府公众号 (cityDepWx)
- `WECHAT_DISTRICT_GOVERNMENT` - 区县政府公众号 (areaDepWx)
- `WECHAT_OVERSEAS_INSTITUTIONS` - 驻境外机构公众号 (jwDepWx)

### 7. APP类 (APP app)
- `NEWS_APPS` - 新闻APP (mainapp)
- `GOVERNMENT_APPS` - 政务APP (zwapp)
- `INDUSTRY_APPS` - 行业APP (hyapp)
- `OTHER_APPS` - 其他APP (appqita)

### 8. 自媒体类 (自媒体 zmt)
- `NEWS_SELF_MEDIA` - 新闻自媒体 (mainzmt)
- `INDUSTRY_SELF_MEDIA` - 行业自媒体 (hyzmt)
- `OTHER_SELF_MEDIA` - 其他自媒体 (zmtqita)

### 9. 新闻网站类 (新闻网站 xw)
- `CENTRAL_NEWS_WEBSITES` - 央级媒体网站 (centerXwweb)
- `PROVINCIAL_NEWS_WEBSITES` - 省级媒体网站 (proXwweb)
- `MUNICIPAL_NEWS_WEBSITES` - 地市媒体网站 (areaXwweb)
- `COMMERCIAL_WEBSITES` - 商业网站 (portalweb)

### 10. 政务网站类 (政务网站 zw)
- `CENTRAL_GOVERNMENT_WEBSITES` - 央级政府网站 (centerDepwz)
- `PROVINCIAL_GOVERNMENT_WEBSITES` - 省级政府网站 (proDepWz)
- `MUNICIPAL_GOVERNMENT_WEBSITES` - 市级政府网站 (cityDepWz)
- `DISTRICT_GOVERNMENT_WEBSITES` - 区县政府网站 (areaDepWz)
- `OVERSEAS_INSTITUTIONS_WEBSITES` - 驻境外机构 (jwDepWz)

## 基本用法

### 1. 获取枚举值和描述
```java
ThirdLevelSourceType type = ThirdLevelSourceType.NATIONAL_KEY_FORUMS;
String value = type.getValue(); // "mainlt"
String description = type.getDescription(); // "全国重点论坛"
```

### 2. 从API值转换
```java
ThirdLevelSourceType type = ThirdLevelSourceType.fromString("mainlt");
// 返回 NATIONAL_KEY_FORUMS

ThirdLevelSourceType defaultType = ThirdLevelSourceType.fromString("invalid");
// 返回 OTHER_APPS (默认值)
```

### 3. 从中文描述转换
```java
ThirdLevelSourceType type = ThirdLevelSourceType.fromChineseDescription("全国重点论坛");
// 返回 NATIONAL_KEY_FORUMS
```

## 业务方法

### 1. 获取对应的二级分类
```java
DetailedSourceType detailedType = ThirdLevelSourceType.NATIONAL_KEY_FORUMS.getDetailedSourceType();
// 返回 DetailedSourceType.FORUMS
```

### 2. 判断来源性质
```java
// 判断是否为政府来源
boolean isGov = ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.isGovernmentSource(); // true

// 判断是否为媒体来源
boolean isMedia = ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.isMediaSource(); // true

// 判断是否为商业来源
boolean isCommercial = ThirdLevelSourceType.WEIBO_COMMERCIAL_MEDIA.isCommercialSource(); // true
```

### 3. 获取权威等级
```java
int authorityLevel = ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.getAuthorityLevel();
// 返回 5 (最高权威等级)

int level = ThirdLevelSourceType.WEIBO_COMMERCIAL_MEDIA.getAuthorityLevel();
// 返回 1 (最低权威等级)
```

### 4. 判断是否为重点来源
```java
boolean isKey = ThirdLevelSourceType.NATIONAL_KEY_FORUMS.isKeySource(); // true
boolean isKey2 = ThirdLevelSourceType.LOCAL_KEY_FORUMS.isKeySource(); // false
```

## 数据库集成

### SQL插入语句
数据库插入语句已在 `src/main/resources/db/三级来源类型 ThirdLevelSourceType.sql` 文件中提供。

### 使用示例
```java
// 存储到数据库
entity.setThirdLevelSourceType(type.getValue()); // 存储API值

// 从数据库查询
String dbValue = entity.getThirdLevelSourceType();
ThirdLevelSourceType type = ThirdLevelSourceType.fromString(dbValue);
```

## 权威等级说明

- **等级5 (最高权威)**: 央级政府和央级媒体
- **等级4 (高权威)**: 省级政府和省级媒体
- **等级3 (中等权威)**: 市级政府和市级媒体
- **等级2 (较低权威)**: 区县政府、重点论坛、重点问答、新闻APP、政务APP
- **等级1 (最低权威)**: 其他所有来源

## 注意事项

1. 所有枚举值都使用String存储，与API保持一致
2. 默认回退值为 `OTHER_APPS`
3. 枚举实现了 `StandardEnum<String>` 接口
4. 支持与现有的 `DetailedSourceType` 和 `SourceType` 的映射关系
5. 提供了丰富的业务判断方法，便于在业务逻辑中使用
