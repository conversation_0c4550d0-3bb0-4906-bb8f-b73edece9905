package com.czb.hn.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ThirdLevelSourceType enumeration
 */
class ThirdLevelSourceTypeTest {

    @Test
    void testEnumValues() {
        // Test that all enum values are properly defined
        assertNotNull(ThirdLevelSourceType.NATIONAL_KEY_FORUMS);
        assertNotNull(ThirdLevelSourceType.LOCAL_KEY_FORUMS);
        assertNotNull(ThirdLevelSourceType.INDUSTRY_FORUMS);
        assertNotNull(ThirdLevelSourceType.SMALL_MEDIUM_FORUMS);

        assertNotNull(ThirdLevelSourceType.KEY_QA_PLATFORMS);
        assertNotNull(ThirdLevelSourceType.OTHER_QA_PLATFORMS);

        assertNotNull(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WEIBO_PROVINCIAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WEIBO_MUNICIPAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WEIBO_COMMERCIAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WEIBO_OTHER_MEDIA);

        assertNotNull(ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WEIBO_PROVINCIAL_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WEIBO_MUNICIPAL_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WEIBO_DISTRICT_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WEIBO_OVERSEAS_INSTITUTIONS);

        assertNotNull(ThirdLevelSourceType.WECHAT_CENTRAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WECHAT_PROVINCIAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WECHAT_MUNICIPAL_MEDIA);
        assertNotNull(ThirdLevelSourceType.WECHAT_COMMERCIAL_MEDIA);

        assertNotNull(ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WECHAT_PROVINCIAL_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WECHAT_MUNICIPAL_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WECHAT_DISTRICT_GOVERNMENT);
        assertNotNull(ThirdLevelSourceType.WECHAT_OVERSEAS_INSTITUTIONS);

        assertNotNull(ThirdLevelSourceType.NEWS_APPS);
        assertNotNull(ThirdLevelSourceType.GOVERNMENT_APPS);
        assertNotNull(ThirdLevelSourceType.INDUSTRY_APPS);
        assertNotNull(ThirdLevelSourceType.OTHER_APPS);

        assertNotNull(ThirdLevelSourceType.NEWS_SELF_MEDIA);
        assertNotNull(ThirdLevelSourceType.INDUSTRY_SELF_MEDIA);
        assertNotNull(ThirdLevelSourceType.OTHER_SELF_MEDIA);

        assertNotNull(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES);
        assertNotNull(ThirdLevelSourceType.PROVINCIAL_NEWS_WEBSITES);
        assertNotNull(ThirdLevelSourceType.MUNICIPAL_NEWS_WEBSITES);
        assertNotNull(ThirdLevelSourceType.COMMERCIAL_WEBSITES);

        assertNotNull(ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES);
        assertNotNull(ThirdLevelSourceType.PROVINCIAL_GOVERNMENT_WEBSITES);
        assertNotNull(ThirdLevelSourceType.MUNICIPAL_GOVERNMENT_WEBSITES);
        assertNotNull(ThirdLevelSourceType.DISTRICT_GOVERNMENT_WEBSITES);
        assertNotNull(ThirdLevelSourceType.OVERSEAS_INSTITUTIONS_WEBSITES);
    }

    @Test
    void testGetValueAndDescription() {
        // Test forum types
        assertEquals("mainlt", ThirdLevelSourceType.NATIONAL_KEY_FORUMS.getValue());
        assertEquals("全国重点论坛", ThirdLevelSourceType.NATIONAL_KEY_FORUMS.getDescription());

        assertEquals("dylt", ThirdLevelSourceType.LOCAL_KEY_FORUMS.getValue());
        assertEquals("地方重点论坛", ThirdLevelSourceType.LOCAL_KEY_FORUMS.getDescription());

        // Test Q&A types
        assertEquals("mainwd", ThirdLevelSourceType.KEY_QA_PLATFORMS.getValue());
        assertEquals("重点问答", ThirdLevelSourceType.KEY_QA_PLATFORMS.getDescription());

        // Test Weibo media types
        assertEquals("centerxwWb", ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.getValue());
        assertEquals("央级媒体号", ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.getDescription());

        // Test WeChat government types
        assertEquals("centerDepWx", ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.getValue());
        assertEquals("央级政府公众号", ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.getDescription());

        // Test app types
        assertEquals("mainapp", ThirdLevelSourceType.NEWS_APPS.getValue());
        assertEquals("新闻APP", ThirdLevelSourceType.NEWS_APPS.getDescription());

        // Test website types
        assertEquals("centerXwweb", ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.getValue());
        assertEquals("央级媒体网站", ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.getDescription());
    }

    @Test
    void testFromString() {
        // Test valid values
        assertEquals(ThirdLevelSourceType.NATIONAL_KEY_FORUMS, ThirdLevelSourceType.fromString("mainlt"));
        assertEquals(ThirdLevelSourceType.KEY_QA_PLATFORMS, ThirdLevelSourceType.fromString("mainwd"));
        assertEquals(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA, ThirdLevelSourceType.fromString("centerxwWb"));
        assertEquals(ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT, ThirdLevelSourceType.fromString("centerDepWx"));
        assertEquals(ThirdLevelSourceType.NEWS_APPS, ThirdLevelSourceType.fromString("mainapp"));
        assertEquals(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES, ThirdLevelSourceType.fromString("centerXwweb"));

        // Test invalid values
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromString("invalid"));
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromString(null));
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromString(""));
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromString("   "));
    }

    @Test
    void testFromChineseDescription() {
        // Test valid descriptions
        assertEquals(ThirdLevelSourceType.NATIONAL_KEY_FORUMS, ThirdLevelSourceType.fromChineseDescription("全国重点论坛"));
        assertEquals(ThirdLevelSourceType.KEY_QA_PLATFORMS, ThirdLevelSourceType.fromChineseDescription("重点问答"));
        assertEquals(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA, ThirdLevelSourceType.fromChineseDescription("央级媒体号"));
        assertEquals(ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT,
                ThirdLevelSourceType.fromChineseDescription("央级政府公众号"));
        assertEquals(ThirdLevelSourceType.NEWS_APPS, ThirdLevelSourceType.fromChineseDescription("新闻APP"));
        assertEquals(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES, ThirdLevelSourceType.fromChineseDescription("央级媒体网站"));

        // Test invalid descriptions
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromChineseDescription("无效描述"));
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromChineseDescription(null));
        assertEquals(ThirdLevelSourceType.OTHER_APPS, ThirdLevelSourceType.fromChineseDescription(""));
    }

    @Test
    void testGetDetailedSourceType() {
        // Test forum mapping
        assertEquals(DetailedSourceType.FORUMS, ThirdLevelSourceType.NATIONAL_KEY_FORUMS.getDetailedSourceType());
        assertEquals(DetailedSourceType.FORUMS, ThirdLevelSourceType.LOCAL_KEY_FORUMS.getDetailedSourceType());

        // Test Q&A mapping
        assertEquals(DetailedSourceType.QA_PLATFORMS, ThirdLevelSourceType.KEY_QA_PLATFORMS.getDetailedSourceType());
        assertEquals(DetailedSourceType.QA_PLATFORMS, ThirdLevelSourceType.OTHER_QA_PLATFORMS.getDetailedSourceType());

        // Test Weibo media mapping
        assertEquals(DetailedSourceType.WEIBO_MEDIA_ACCOUNTS,
                ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.getDetailedSourceType());
        assertEquals(DetailedSourceType.WEIBO_MEDIA_ACCOUNTS,
                ThirdLevelSourceType.WEIBO_PROVINCIAL_MEDIA.getDetailedSourceType());

        // Test Weibo government mapping
        assertEquals(DetailedSourceType.WEIBO_GOVERNMENT_ACCOUNTS,
                ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.getDetailedSourceType());
        assertEquals(DetailedSourceType.WEIBO_GOVERNMENT_ACCOUNTS,
                ThirdLevelSourceType.WEIBO_PROVINCIAL_GOVERNMENT.getDetailedSourceType());

        // Test WeChat media mapping
        assertEquals(DetailedSourceType.WECHAT_MEDIA_ACCOUNTS,
                ThirdLevelSourceType.WECHAT_CENTRAL_MEDIA.getDetailedSourceType());
        assertEquals(DetailedSourceType.WECHAT_MEDIA_ACCOUNTS,
                ThirdLevelSourceType.WECHAT_PROVINCIAL_MEDIA.getDetailedSourceType());

        // Test WeChat government mapping
        assertEquals(DetailedSourceType.WECHAT_GOVERNMENT_ACCOUNTS,
                ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.getDetailedSourceType());
        assertEquals(DetailedSourceType.WECHAT_GOVERNMENT_ACCOUNTS,
                ThirdLevelSourceType.WECHAT_PROVINCIAL_GOVERNMENT.getDetailedSourceType());

        // Test app mapping
        assertEquals(DetailedSourceType.MOBILE_APPLICATIONS, ThirdLevelSourceType.NEWS_APPS.getDetailedSourceType());
        assertEquals(DetailedSourceType.MOBILE_APPLICATIONS,
                ThirdLevelSourceType.GOVERNMENT_APPS.getDetailedSourceType());

        // Test self media mapping
        assertEquals(DetailedSourceType.SELF_MEDIA_PLATFORMS,
                ThirdLevelSourceType.NEWS_SELF_MEDIA.getDetailedSourceType());
        assertEquals(DetailedSourceType.SELF_MEDIA_PLATFORMS,
                ThirdLevelSourceType.INDUSTRY_SELF_MEDIA.getDetailedSourceType());

        // Test news website mapping
        assertEquals(DetailedSourceType.NEWS_WEBSITES,
                ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.getDetailedSourceType());
        assertEquals(DetailedSourceType.NEWS_WEBSITES,
                ThirdLevelSourceType.PROVINCIAL_NEWS_WEBSITES.getDetailedSourceType());

        // Test government website mapping
        assertEquals(DetailedSourceType.GOVERNMENT_WEBSITES,
                ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES.getDetailedSourceType());
        assertEquals(DetailedSourceType.GOVERNMENT_WEBSITES,
                ThirdLevelSourceType.PROVINCIAL_GOVERNMENT_WEBSITES.getDetailedSourceType());
    }

    @Test
    void testIsGovernmentSource() {
        // Test government sources
        assertTrue(ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.isGovernmentSource());
        assertTrue(ThirdLevelSourceType.WEIBO_PROVINCIAL_GOVERNMENT.isGovernmentSource());
        assertTrue(ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.isGovernmentSource());
        assertTrue(ThirdLevelSourceType.WECHAT_PROVINCIAL_GOVERNMENT.isGovernmentSource());
        assertTrue(ThirdLevelSourceType.GOVERNMENT_APPS.isGovernmentSource());
        assertTrue(ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES.isGovernmentSource());
        assertTrue(ThirdLevelSourceType.PROVINCIAL_GOVERNMENT_WEBSITES.isGovernmentSource());

        // Test non-government sources
        assertFalse(ThirdLevelSourceType.NATIONAL_KEY_FORUMS.isGovernmentSource());
        assertFalse(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.isGovernmentSource());
        assertFalse(ThirdLevelSourceType.WECHAT_CENTRAL_MEDIA.isGovernmentSource());
        assertFalse(ThirdLevelSourceType.NEWS_APPS.isGovernmentSource());
        assertFalse(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.isGovernmentSource());
    }

    @Test
    void testIsMediaSource() {
        // Test media sources
        assertTrue(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.isMediaSource());
        assertTrue(ThirdLevelSourceType.WEIBO_PROVINCIAL_MEDIA.isMediaSource());
        assertTrue(ThirdLevelSourceType.WECHAT_CENTRAL_MEDIA.isMediaSource());
        assertTrue(ThirdLevelSourceType.WECHAT_PROVINCIAL_MEDIA.isMediaSource());
        assertTrue(ThirdLevelSourceType.NEWS_APPS.isMediaSource());
        assertTrue(ThirdLevelSourceType.NEWS_SELF_MEDIA.isMediaSource());
        assertTrue(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.isMediaSource());
        assertTrue(ThirdLevelSourceType.PROVINCIAL_NEWS_WEBSITES.isMediaSource());

        // Test non-media sources
        assertFalse(ThirdLevelSourceType.NATIONAL_KEY_FORUMS.isMediaSource());
        assertFalse(ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.isMediaSource());
        assertFalse(ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.isMediaSource());
        assertFalse(ThirdLevelSourceType.GOVERNMENT_APPS.isMediaSource());
        assertFalse(ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES.isMediaSource());
    }

    @Test
    void testIsCommercialSource() {
        // Test commercial sources
        assertTrue(ThirdLevelSourceType.WEIBO_COMMERCIAL_MEDIA.isCommercialSource());
        assertTrue(ThirdLevelSourceType.WECHAT_COMMERCIAL_MEDIA.isCommercialSource());
        assertTrue(ThirdLevelSourceType.COMMERCIAL_WEBSITES.isCommercialSource());

        // Test non-commercial sources
        assertFalse(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.isCommercialSource());
        assertFalse(ThirdLevelSourceType.WECHAT_CENTRAL_MEDIA.isCommercialSource());
        assertFalse(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.isCommercialSource());
        assertFalse(ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES.isCommercialSource());
    }

    @Test
    void testGetAuthorityLevel() {
        // Test highest authority (level 5)
        assertEquals(5, ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.getAuthorityLevel());
        assertEquals(5, ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.getAuthorityLevel());
        assertEquals(5, ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES.getAuthorityLevel());
        assertEquals(5, ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.getAuthorityLevel());

        // Test high authority (level 4)
        assertEquals(4, ThirdLevelSourceType.WEIBO_PROVINCIAL_GOVERNMENT.getAuthorityLevel());
        assertEquals(4, ThirdLevelSourceType.WECHAT_PROVINCIAL_GOVERNMENT.getAuthorityLevel());
        assertEquals(4, ThirdLevelSourceType.PROVINCIAL_GOVERNMENT_WEBSITES.getAuthorityLevel());
        assertEquals(4, ThirdLevelSourceType.PROVINCIAL_NEWS_WEBSITES.getAuthorityLevel());

        // Test medium authority (level 3)
        assertEquals(3, ThirdLevelSourceType.WEIBO_MUNICIPAL_GOVERNMENT.getAuthorityLevel());
        assertEquals(3, ThirdLevelSourceType.WECHAT_MUNICIPAL_GOVERNMENT.getAuthorityLevel());
        assertEquals(3, ThirdLevelSourceType.MUNICIPAL_GOVERNMENT_WEBSITES.getAuthorityLevel());
        assertEquals(3, ThirdLevelSourceType.MUNICIPAL_NEWS_WEBSITES.getAuthorityLevel());

        // Test lower authority (level 2)
        assertEquals(2, ThirdLevelSourceType.WEIBO_DISTRICT_GOVERNMENT.getAuthorityLevel());
        assertEquals(2, ThirdLevelSourceType.WECHAT_DISTRICT_GOVERNMENT.getAuthorityLevel());
        assertEquals(2, ThirdLevelSourceType.DISTRICT_GOVERNMENT_WEBSITES.getAuthorityLevel());
        assertEquals(2, ThirdLevelSourceType.NATIONAL_KEY_FORUMS.getAuthorityLevel());
        assertEquals(2, ThirdLevelSourceType.KEY_QA_PLATFORMS.getAuthorityLevel());
        assertEquals(2, ThirdLevelSourceType.NEWS_APPS.getAuthorityLevel());
        assertEquals(2, ThirdLevelSourceType.GOVERNMENT_APPS.getAuthorityLevel());

        // Test lowest authority (level 1)
        assertEquals(1, ThirdLevelSourceType.LOCAL_KEY_FORUMS.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.INDUSTRY_FORUMS.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.SMALL_MEDIUM_FORUMS.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.OTHER_QA_PLATFORMS.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.WEIBO_COMMERCIAL_MEDIA.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.WECHAT_COMMERCIAL_MEDIA.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.INDUSTRY_APPS.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.OTHER_APPS.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.INDUSTRY_SELF_MEDIA.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.OTHER_SELF_MEDIA.getAuthorityLevel());
        assertEquals(1, ThirdLevelSourceType.COMMERCIAL_WEBSITES.getAuthorityLevel());
    }

    @Test
    void testIsKeySource() {
        // Test key sources
        assertTrue(ThirdLevelSourceType.NATIONAL_KEY_FORUMS.isKeySource());
        assertTrue(ThirdLevelSourceType.KEY_QA_PLATFORMS.isKeySource());
        assertTrue(ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.isKeySource());
        assertTrue(ThirdLevelSourceType.WEIBO_CENTRAL_GOVERNMENT.isKeySource());
        assertTrue(ThirdLevelSourceType.WECHAT_CENTRAL_MEDIA.isKeySource());
        assertTrue(ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.isKeySource());
        assertTrue(ThirdLevelSourceType.NEWS_APPS.isKeySource());
        assertTrue(ThirdLevelSourceType.GOVERNMENT_APPS.isKeySource());
        assertTrue(ThirdLevelSourceType.NEWS_SELF_MEDIA.isKeySource());
        assertTrue(ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.isKeySource());
        assertTrue(ThirdLevelSourceType.CENTRAL_GOVERNMENT_WEBSITES.isKeySource());

        // Test non-key sources
        assertFalse(ThirdLevelSourceType.LOCAL_KEY_FORUMS.isKeySource());
        assertFalse(ThirdLevelSourceType.INDUSTRY_FORUMS.isKeySource());
        assertFalse(ThirdLevelSourceType.SMALL_MEDIUM_FORUMS.isKeySource());
        assertFalse(ThirdLevelSourceType.OTHER_QA_PLATFORMS.isKeySource());
        assertFalse(ThirdLevelSourceType.WEIBO_PROVINCIAL_MEDIA.isKeySource());
        assertFalse(ThirdLevelSourceType.WEIBO_MUNICIPAL_MEDIA.isKeySource());
        assertFalse(ThirdLevelSourceType.WEIBO_COMMERCIAL_MEDIA.isKeySource());
        assertFalse(ThirdLevelSourceType.WEIBO_OTHER_MEDIA.isKeySource());
        assertFalse(ThirdLevelSourceType.INDUSTRY_APPS.isKeySource());
        assertFalse(ThirdLevelSourceType.OTHER_APPS.isKeySource());
        assertFalse(ThirdLevelSourceType.INDUSTRY_SELF_MEDIA.isKeySource());
        assertFalse(ThirdLevelSourceType.OTHER_SELF_MEDIA.isKeySource());
        assertFalse(ThirdLevelSourceType.PROVINCIAL_NEWS_WEBSITES.isKeySource());
        assertFalse(ThirdLevelSourceType.MUNICIPAL_NEWS_WEBSITES.isKeySource());
        assertFalse(ThirdLevelSourceType.COMMERCIAL_WEBSITES.isKeySource());
    }

    @Test
    void testToString() {
        assertEquals("全国重点论坛", ThirdLevelSourceType.NATIONAL_KEY_FORUMS.toString());
        assertEquals("重点问答", ThirdLevelSourceType.KEY_QA_PLATFORMS.toString());
        assertEquals("央级媒体号", ThirdLevelSourceType.WEIBO_CENTRAL_MEDIA.toString());
        assertEquals("央级政府公众号", ThirdLevelSourceType.WECHAT_CENTRAL_GOVERNMENT.toString());
        assertEquals("新闻APP", ThirdLevelSourceType.NEWS_APPS.toString());
        assertEquals("央级媒体网站", ThirdLevelSourceType.CENTRAL_NEWS_WEBSITES.toString());
    }

    @Test
    void testEnumCount() {
        // Verify we have all 41 expected enum values
        assertEquals(41, ThirdLevelSourceType.values().length);
    }
}
