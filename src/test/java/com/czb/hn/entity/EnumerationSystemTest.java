package com.czb.hn.entity;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.DetailedSourceType;
import com.czb.hn.enums.EmotionType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MatchType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.ResultViewType;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.UserVerificationType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for the standardized enumeration system
 * Tests all enum classes for consistency with Sina API specifications
 */
@DisplayName("Enumeration System Tests")
public class EnumerationSystemTest {

    @Test
    @DisplayName("UserVerificationType - Test conversion from integer values")
    void testUserVerificationTypeConversion() {
        // Test valid conversions
        assertEquals(UserVerificationType.REGULAR, UserVerificationType.fromInteger(-1));
        assertEquals(UserVerificationType.ORANGE_V, UserVerificationType.fromInteger(0));
        assertEquals(UserVerificationType.BLUE_V, UserVerificationType.fromInteger(1));
        assertEquals(UserVerificationType.BLUE_V, UserVerificationType.fromInteger(2)); // All Blue V types merged
        assertEquals(UserVerificationType.BLUE_V, UserVerificationType.fromInteger(7)); // All Blue V types merged
        assertEquals(UserVerificationType.EXPERT, UserVerificationType.fromInteger(200));
        assertEquals(UserVerificationType.EXPERT, UserVerificationType.fromInteger(220)); // All Expert types merged
        assertEquals(UserVerificationType.GOLD_V, UserVerificationType.fromInteger(600));

        // Test null and invalid values
        assertEquals(UserVerificationType.REGULAR, UserVerificationType.fromInteger(null));
        assertEquals(UserVerificationType.REGULAR, UserVerificationType.fromInteger(999));

        // Test string conversion
        assertEquals(UserVerificationType.BLUE_V, UserVerificationType.fromString("1"));
        assertEquals(UserVerificationType.BLUE_V, UserVerificationType.fromString("3")); // All Blue V types merged
        assertEquals(UserVerificationType.EXPERT, UserVerificationType.fromString("200"));
        assertEquals(UserVerificationType.REGULAR, UserVerificationType.fromString("invalid"));
        assertEquals(UserVerificationType.REGULAR, UserVerificationType.fromString(null));
    }

    @Test
    @DisplayName("EmotionType - Test conversion from Chinese values")
    void testEmotionTypeConversion() {
        // Test valid conversions
        assertEquals(EmotionType.NEUTRAL, EmotionType.fromChineseValue("中性"));
        assertEquals(EmotionType.JOY, EmotionType.fromChineseValue("喜悦"));
        assertEquals(EmotionType.SADNESS, EmotionType.fromChineseValue("悲伤"));
        assertEquals(EmotionType.ANGER, EmotionType.fromChineseValue("愤怒"));
        assertEquals(EmotionType.SURPRISE, EmotionType.fromChineseValue("惊奇"));
        assertEquals(EmotionType.FEAR, EmotionType.fromChineseValue("恐惧"));

        // Test null and invalid values
        assertEquals(EmotionType.NEUTRAL, EmotionType.fromChineseValue(null));
        assertEquals(EmotionType.NEUTRAL, EmotionType.fromChineseValue(""));
        assertEquals(EmotionType.NEUTRAL, EmotionType.fromChineseValue("invalid"));

        // Test sentiment scores
        assertEquals(0.5, EmotionType.NEUTRAL.getSentimentScore());
        assertEquals(0.8, EmotionType.JOY.getSentimentScore());
        assertEquals(0.2, EmotionType.SADNESS.getSentimentScore());

        // Test polarity
        assertEquals(1, EmotionType.JOY.getPolarity());
        assertEquals(-1, EmotionType.ANGER.getPolarity());
        assertEquals(0, EmotionType.NEUTRAL.getPolarity());
    }

    @Test
    @DisplayName("InformationSensitivityType - Test conversion and validation")
    void testInformationSensitivityTypeConversion() {
        // Test valid conversions (移除ALL，因为新设计不包含ALL)
        assertEquals(InformationSensitivityType.SENSITIVE, InformationSensitivityType.fromInteger(1));
        assertEquals(InformationSensitivityType.NON_SENSITIVE, InformationSensitivityType.fromInteger(2));
        assertEquals(InformationSensitivityType.NEUTRAL, InformationSensitivityType.fromInteger(3));

        // Test string conversion
        assertEquals(InformationSensitivityType.SENSITIVE, InformationSensitivityType.fromString("1"));
        assertEquals(InformationSensitivityType.NEUTRAL, InformationSensitivityType.fromString("invalid"));

        // Test validation methods
        assertTrue(InformationSensitivityType.SENSITIVE.isSensitive());
        assertFalse(InformationSensitivityType.NON_SENSITIVE.isSensitive());
        assertTrue(InformationSensitivityType.NON_SENSITIVE.isNonSensitive());
        assertTrue(InformationSensitivityType.NEUTRAL.isNeutral());
    }

    @Test
    @DisplayName("ContentCategory - Test conversion and validation")
    void testContentCategoryConversion() {
        // Test valid conversions
        assertEquals(ContentCategory.ORIGINAL, ContentCategory.fromInteger(1));
        assertEquals(ContentCategory.FORWARD, ContentCategory.fromInteger(2));

        // Test validation methods
        assertTrue(ContentCategory.ORIGINAL.isOriginal());
        assertFalse(ContentCategory.FORWARD.isOriginal());
        assertTrue(ContentCategory.FORWARD.isForward());
        assertFalse(ContentCategory.ORIGINAL.isForward());

        // Test integer conversion
        assertEquals(Integer.valueOf(1), ContentCategory.ORIGINAL.toInteger());
        assertEquals(Integer.valueOf(2), ContentCategory.FORWARD.toInteger());
    }

    @Test
    @DisplayName("ResultViewType - Test conversion and validation")
    void testResultViewTypeConversion() {
        // Test valid conversions
        assertEquals(ResultViewType.NORMAL, ResultViewType.fromInteger(1));
        assertEquals(ResultViewType.NOISE, ResultViewType.fromInteger(2));

        // Test string conversion
        assertEquals(ResultViewType.NORMAL, ResultViewType.fromString("1"));
        assertEquals(ResultViewType.NOISE, ResultViewType.fromString("2"));
        assertEquals(ResultViewType.NORMAL, ResultViewType.fromString("invalid"));

        // Test validation methods
        assertTrue(ResultViewType.NORMAL.isValid());
        assertFalse(ResultViewType.NOISE.isValid());
        assertTrue(ResultViewType.NOISE.isNoise());
        assertFalse(ResultViewType.NORMAL.isNoise());

        // Test quality scores
        assertEquals(1.0, ResultViewType.NORMAL.getQualityScore());
        assertEquals(0.0, ResultViewType.NOISE.getQualityScore());
    }

    @Test
    @DisplayName("SourceType - Test conversion and validation")
    void testSourceTypeConversion() {
        // Test valid conversions
        assertEquals(SourceType.INTERACTIVE_FORUMS, SourceType.fromString("hdlt"));
        assertEquals(SourceType.WEIBO, SourceType.fromString("wb"));
        assertEquals(SourceType.WECHAT, SourceType.fromString("wx"));
        assertEquals(SourceType.MOBILE_APPS, SourceType.fromString("zmtapp"));
        assertEquals(SourceType.VIDEO, SourceType.fromString("sp"));
        assertEquals(SourceType.DIGITAL_NEWSPAPERS, SourceType.fromString("szb"));
        assertEquals(SourceType.WEBSITES, SourceType.fromString("wz"));

        // Test validation methods
        assertTrue(SourceType.WEIBO.isSocialMedia());
        assertTrue(SourceType.WECHAT.isSocialMedia());
        assertFalse(SourceType.WEBSITES.isSocialMedia());

        assertTrue(SourceType.WEBSITES.isTraditionalMedia());
        assertTrue(SourceType.DIGITAL_NEWSPAPERS.isTraditionalMedia());
        assertFalse(SourceType.WEIBO.isTraditionalMedia());

        // Test credibility scores
        assertEquals(0.9, SourceType.DIGITAL_NEWSPAPERS.getCredibilityScore());
        assertEquals(0.8, SourceType.WEBSITES.getCredibilityScore());
        assertEquals(0.6, SourceType.WEIBO.getCredibilityScore());
    }

    @Test
    @DisplayName("mediaLevels - Test conversion and validation")
    void testMediaLevelConversion() {
        // Test valid conversions
        assertEquals(MediaLevel.NATIONAL, MediaLevel.fromChineseValue("央级"));
        assertEquals(MediaLevel.PROVINCIAL, MediaLevel.fromChineseValue("省级"));
        assertEquals(MediaLevel.MUNICIPAL, MediaLevel.fromChineseValue("地市"));
        assertEquals(MediaLevel.SMALL_MEDIUM, MediaLevel.fromChineseValue("中小"));
        assertEquals(MediaLevel.CORPORATE, MediaLevel.fromChineseValue("企业资讯"));

        // Test validation methods
        assertTrue(MediaLevel.NATIONAL.isGovernmentLevel());
        assertTrue(MediaLevel.PROVINCIAL.isGovernmentLevel());
        assertFalse(MediaLevel.CORPORATE.isGovernmentLevel());

        assertTrue(MediaLevel.CORPORATE.isCommercial());
        assertFalse(MediaLevel.NATIONAL.isCommercial());

        // Test authority levels
        assertEquals(6, MediaLevel.NATIONAL.getAuthorityLevel());
        assertEquals(5, MediaLevel.PROVINCIAL.getAuthorityLevel());
        assertEquals(1, MediaLevel.CORPORATE.getAuthorityLevel());
    }

    @Test
    @DisplayName("ContentType - Test conversion and validation")
    void testContentTypeConversion() {
        // Test valid conversions
        assertEquals(ContentType.TEXT, ContentType.fromString("1"));
        assertEquals(ContentType.IMAGE, ContentType.fromString("2"));
        assertEquals(ContentType.SHORT_URL, ContentType.fromString("3"));
        assertEquals(ContentType.VIDEO, ContentType.fromString("4"));

        // Test validation methods
        assertTrue(ContentType.IMAGE.isMultimedia());
        assertTrue(ContentType.VIDEO.isMultimedia());
        assertFalse(ContentType.TEXT.isMultimedia());

        assertTrue(ContentType.TEXT.isTextBased());
        assertTrue(ContentType.SHORT_URL.isTextBased());
        assertFalse(ContentType.VIDEO.isTextBased());

        // Test processing priorities
        assertEquals(4, ContentType.TEXT.getProcessingPriority());
        assertEquals(1, ContentType.VIDEO.getProcessingPriority());
    }

    @Test
    @DisplayName("MatchType - Test conversion and validation")
    void testMatchTypeConversion() {
        // Test valid conversions
        assertEquals(MatchType.KEYWORD, MatchType.fromString("1"));
        assertEquals(MatchType.USER, MatchType.fromString("2"));

        // Test validation methods
        assertTrue(MatchType.KEYWORD.isKeywordMatch());
        assertFalse(MatchType.USER.isKeywordMatch());
        assertTrue(MatchType.USER.isUserMatch());
        assertFalse(MatchType.KEYWORD.isUserMatch());

        // Test precision levels
        assertEquals(2, MatchType.USER.getPrecisionLevel());
        assertEquals(1, MatchType.KEYWORD.getPrecisionLevel());
    }

    @Test
    @DisplayName("DetailedSourceType - Test conversion and validation")
    void testDetailedSourceTypeConversion() {
        // Test valid conversions
        assertEquals(DetailedSourceType.NEWS_WEBSITES, DetailedSourceType.fromString("xw"));
        assertEquals(DetailedSourceType.WEIBO_MEDIA_ACCOUNTS, DetailedSourceType.fromString("wbxw"));
        assertEquals(DetailedSourceType.WECHAT_MEDIA_ACCOUNTS, DetailedSourceType.fromString("wxxw"));

        // Test parent type relationships - removed getParentSourceType() method
        // These relationships are now implicit in the enum design

        // Test validation methods
        assertTrue(DetailedSourceType.GOVERNMENT_WEBSITES.isGovernmentSource());
        assertTrue(DetailedSourceType.WEIBO_GOVERNMENT_ACCOUNTS.isGovernmentSource());
        assertFalse(DetailedSourceType.WEIBO_PERSONAL_ACCOUNTS.isGovernmentSource());

        assertTrue(DetailedSourceType.NEWS_WEBSITES.isMediaSource());
        assertTrue(DetailedSourceType.WEIBO_MEDIA_ACCOUNTS.isMediaSource());
        assertFalse(DetailedSourceType.WEIBO_PERSONAL_ACCOUNTS.isMediaSource());

        assertTrue(DetailedSourceType.WEIBO_PERSONAL_ACCOUNTS.isPersonalSource());
        assertFalse(DetailedSourceType.NEWS_WEBSITES.isPersonalSource());

        // Test credibility scores - removed getCredibilityScore() method
        // Credibility assessment is now handled by business logic
    }

    @Test
    @DisplayName("Enum Ordinal Consistency - Test database storage compatibility")
    void testEnumOrdinalConsistency() {
        // Test that enum ordinals are consistent (注意：新设计不使用序号，使用getValue())
        assertEquals(0, InformationSensitivityType.SENSITIVE.ordinal());
        assertEquals(1, InformationSensitivityType.NON_SENSITIVE.ordinal());
        assertEquals(2, InformationSensitivityType.NEUTRAL.ordinal());

        assertEquals(0, ContentCategory.ORIGINAL.ordinal());
        assertEquals(1, ContentCategory.FORWARD.ordinal());

        assertEquals(0, EmotionType.NEUTRAL.ordinal());
        assertEquals(1, EmotionType.JOY.ordinal());

        // Verify that ordinals match expected database values
        assertNotNull(InformationSensitivityType.values());
        assertNotNull(ContentCategory.values());
        assertNotNull(EmotionType.values());
    }
}
