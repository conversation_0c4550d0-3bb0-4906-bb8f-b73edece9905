package com.czb.hn.integration;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.DetailedSourceType;
import com.czb.hn.enums.EmotionType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MatchType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.ResultViewType;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.UserVerificationType;
import com.czb.hn.jpa.securadar.entity.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the enumeration system
 * Tests enum functionality and entity integration
 */
@DisplayName("Enumeration System Integration Tests")
public class EnumerationIntegrationTest {

    private SinaNewsOdsEntity testOdsEntity;
    private SinaNewsDwdEntity testDwdEntity;
    private AlertResult testAlertResult;

    @BeforeEach
    void setUp() {

        // Create test ODS entity with enum values
        testOdsEntity = new SinaNewsOdsEntity();
        testOdsEntity.setId(1L);
        testOdsEntity.setContentId("test_content_123");
        testOdsEntity.setTitle("测试新闻标题");
        testOdsEntity.setEmotion(EmotionType.JOY);
        testOdsEntity.setSensitivityType(InformationSensitivityType.SENSITIVE);
        testOdsEntity.setIsOriginal(ContentCategory.ORIGINAL);
        testOdsEntity.setResultView(ResultViewType.NORMAL);
        testOdsEntity.setContentTypes(ContentType.TEXT);
        testOdsEntity.setUserVerifiedType(UserVerificationType.BLUE_V);

        // Create test DWD entity with enum values
        testDwdEntity = new SinaNewsDwdEntity();
        testDwdEntity.setId(1L);
        testDwdEntity.setContentId("test_content_123");
        testDwdEntity.setTitle("测试新闻标题");
        testDwdEntity.setEmotion(EmotionType.JOY);
        testDwdEntity.setSensitivityType(InformationSensitivityType.SENSITIVE);
        testDwdEntity.setMediaType(SourceType.WEIBO);
        testDwdEntity.setMediaTypeSecond(DetailedSourceType.WEIBO_MEDIA_ACCOUNTS);
        testDwdEntity.setMediaLevel(MediaLevel.PROVINCIAL);
        testDwdEntity.setAuthorVerifiedType(UserVerificationType.BLUE_V);
        testDwdEntity.setInfoLevel(MediaLevel.MUNICIPAL);
        testDwdEntity.setMatchType(MatchType.KEYWORD);
        testDwdEntity.setResultView(ResultViewType.NORMAL);
        testDwdEntity.setContentTypes(ContentType.TEXT);

        // Create test alert result
        testAlertResult = new AlertResult();
        testAlertResult.setId(1L);
        testAlertResult.setEnterpriseId("enterprise-001");
        testAlertResult.setPlanId(100L);
        testAlertResult.setConfigurationId(1L);
        testAlertResult.setTitle("测试告警");
        testAlertResult.setContent("测试告警内容");
        testAlertResult.setInformationSensitivityType(InformationSensitivityType.SENSITIVE);
        testAlertResult.setContentCategory(ContentCategory.ORIGINAL);
        testAlertResult.setWarningLevel(AlertResult.WarningLevel.SEVERE);
        testAlertResult.setSource("微博");
        testAlertResult.setWarningTime(LocalDateTime.now());
    }

    @Test
    @DisplayName("Test enum conversion in data collection flow")
    void testEnumConversionInDataCollection() {
        // Test that enum values are properly stored and retrieved
        assertNotNull(testOdsEntity.getEmotion());
        assertEquals(EmotionType.JOY, testOdsEntity.getEmotion());
        assertEquals("喜悦", testOdsEntity.getEmotion().getDescription());
        assertEquals(0.8, testOdsEntity.getEmotion().getSentimentScore());

        assertNotNull(testOdsEntity.getSensitivityType());
        assertEquals(InformationSensitivityType.SENSITIVE, testOdsEntity.getSensitivityType());
        assertTrue(testOdsEntity.getSensitivityType().isSensitive());

        assertNotNull(testOdsEntity.getIsOriginal());
        assertEquals(ContentCategory.ORIGINAL, testOdsEntity.getIsOriginal());
        assertTrue(testOdsEntity.getIsOriginal().isOriginal());

        assertNotNull(testOdsEntity.getUserVerifiedType());
        assertEquals(UserVerificationType.BLUE_V, testOdsEntity.getUserVerifiedType());
        assertTrue(testOdsEntity.getUserVerifiedType().isBlueV());
    }

    @Test
    @DisplayName("Test enum conversion in data cleaning flow")
    void testEnumConversionInDataCleaning() {
        // Test DWD entity enum values
        assertNotNull(testDwdEntity.getMediaType());
        assertEquals(SourceType.WEIBO, testDwdEntity.getMediaType());
        assertTrue(testDwdEntity.getMediaType().isSocialMedia());
        assertEquals(0.6, testDwdEntity.getMediaType().getCredibilityScore());

        assertNotNull(testDwdEntity.getMediaTypeSecond());
        assertEquals(DetailedSourceType.WEIBO_MEDIA_ACCOUNTS, testDwdEntity.getMediaTypeSecond());
        assertTrue(testDwdEntity.getMediaTypeSecond().isMediaSource());
        // Parent source type relationship is now implicit in the enum design

        assertNotNull(testDwdEntity.getMediaLevel());
        assertEquals(MediaLevel.PROVINCIAL, testDwdEntity.getMediaLevel());
        assertTrue(testDwdEntity.getMediaLevel().isGovernmentLevel());
        assertEquals(5, testDwdEntity.getMediaLevel().getAuthorityLevel());

        assertNotNull(testDwdEntity.getMatchType());
        assertEquals(MatchType.KEYWORD, testDwdEntity.getMatchType());
        assertTrue(testDwdEntity.getMatchType().isKeywordMatch());
        assertEquals(1, testDwdEntity.getMatchType().getPrecisionLevel());
    }

    @Test
    @DisplayName("Test enum value storage consistency")
    void testEnumValueStorageConsistency() {
        // Test that enum values match expected API values (not ordinals)
        assertEquals(Integer.valueOf(1), InformationSensitivityType.SENSITIVE.getValue());
        assertEquals(Integer.valueOf(1), ContentCategory.ORIGINAL.getValue());
        assertEquals("喜悦", EmotionType.JOY.getValue());
        assertEquals(Integer.valueOf(1), ResultViewType.NORMAL.getValue());
        assertEquals("1", ContentType.TEXT.getValue());

        // Test that values can be used for database queries
        Integer sensitivityValue = testAlertResult.getInformationSensitivityType().getValue();
        assertEquals(Integer.valueOf(1), sensitivityValue);

        Integer categoryValue = testAlertResult.getContentCategory().getValue();
        assertEquals(Integer.valueOf(1), categoryValue);
    }

    @Test
    @DisplayName("Test enum validation and error handling")
    void testEnumValidationAndErrorHandling() {
        // Test invalid enum conversion
        InformationSensitivityType invalidSensitivity = InformationSensitivityType.fromString("INVALID");
        assertEquals(InformationSensitivityType.NEUTRAL, invalidSensitivity);

        ContentCategory invalidCategory = ContentCategory.fromString("INVALID");
        assertEquals(ContentCategory.ORIGINAL, invalidCategory); // 默认值改为ORIGINAL

        EmotionType invalidEmotion = EmotionType.fromChineseValue("无效情感");
        assertEquals(EmotionType.NEUTRAL, invalidEmotion);

        UserVerificationType invalidUser = UserVerificationType.fromInteger(999);
        assertEquals(UserVerificationType.REGULAR, invalidUser);

        // Test null handling
        assertNotNull(InformationSensitivityType.fromInteger(null));
        assertNotNull(ContentCategory.fromInteger(null));
        assertNotNull(EmotionType.fromChineseValue(null));
        assertNotNull(UserVerificationType.fromString(null));
    }

    @Test
    @DisplayName("Test enum business logic methods")
    void testEnumBusinessLogicMethods() {
        // Test SourceType business methods
        assertTrue(SourceType.WEIBO.isSocialMedia());
        assertFalse(SourceType.WEBSITES.isSocialMedia());
        assertTrue(SourceType.DIGITAL_NEWSPAPERS.isTraditionalMedia());

        // Test mediaLevels business methods
        assertTrue(MediaLevel.NATIONAL.isGovernmentLevel());
        assertFalse(MediaLevel.CORPORATE.isGovernmentLevel());
        assertTrue(MediaLevel.CORPORATE.isCommercial());

        // Test ContentType business methods
        assertTrue(ContentType.VIDEO.isMultimedia());
        assertFalse(ContentType.TEXT.isMultimedia());
        assertTrue(ContentType.TEXT.isTextBased());

        // Test DetailedSourceType business methods
        assertTrue(DetailedSourceType.GOVERNMENT_WEBSITES.isGovernmentSource());
        assertTrue(DetailedSourceType.NEWS_WEBSITES.isMediaSource());
        assertTrue(DetailedSourceType.WEIBO_PERSONAL_ACCOUNTS.isPersonalSource());
    }
}
