package com.czb.hn.util;

import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.enums.*;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for AlertResultMapper enum value conversion
 * 测试AlertResultMapper枚举值转换功能
 */
@SpringBootTest
class AlertResultMapperEnumValueTest {

    @Autowired
    private AlertResultMapper alertResultMapper;

    private AlertResult testEntity;

    @BeforeEach
    void setUp() {
        testEntity = AlertResult.builder()
                .id(1L)
                .enterpriseId("enterprise-001")
                .planId(100L)
                .configurationId(1L)
                .title("测试告警")
                .content("测试内容")
                .involvedKeywords("[{\"keyword\":\"关键词1\",\"count\":3}]")
                .informationSensitivityType(InformationSensitivityType.SENSITIVE)
                .contentCategory(ContentCategory.ORIGINAL)
                .sourceType(SourceType.WEIBO)
                .contentType(ContentType.TEXT)
                .contentMatchType(ContentMatchType.MAIN_TXT)
                .mediaLevel(MediaLevel.NATIONAL)
                .warningLevel(AlertResult.WarningLevel.SEVERE)
                .source("微博")
                .provincial("北京")
                .warningTime(LocalDateTime.now())
                .similarArticleCount(5)
                .originalContentId("content_123456")
                .extendedAttributes("{}")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    @Test
    void testToResponseDto_ShouldReturnEnumValues() {
        // 执行转换
        AlertResultResponseDto result = alertResultMapper.toResponseDto(testEntity);

        // 验证结果不为空
        assertNotNull(result);

        // 验证枚举字段返回的是value值，而不是枚举名称
        assertEquals(InformationSensitivityType.SENSITIVE.getValue(), result.informationSensitivityType(),
                "信息敏感性类型应该返回value值: " + InformationSensitivityType.SENSITIVE.getValue());

        assertEquals(ContentCategory.ORIGINAL.getValue(), result.contentCategory(),
                "内容类别应该返回value值: " + ContentCategory.ORIGINAL.getValue());

        assertEquals(SourceType.WEIBO.getValue(), result.sourceType(),
                "来源类型应该返回value值: " + SourceType.WEIBO.getValue());

        assertEquals(ContentType.TEXT.getValue(), result.contentType(),
                "内容类型应该返回value值: " + ContentType.TEXT.getValue());

        assertEquals(ContentMatchType.MAIN_TXT.getValue(), result.contentMatchType(),
                "内容匹配类型应该返回value值: " + ContentMatchType.MAIN_TXT.getValue());

        assertEquals(MediaLevel.NATIONAL.getValue(), result.mediaLevel(),
                "媒体级别应该返回value值: " + MediaLevel.NATIONAL.getValue());

        assertEquals(AlertResult.WarningLevel.SEVERE.name(), result.warningLevel(),
                "预警级别应该返回name值: " + AlertResult.WarningLevel.SEVERE.name());

        // 验证其他字段正常
        assertEquals(testEntity.getId(), result.id());
        assertEquals(testEntity.getTitle(), result.title());
        assertEquals(testEntity.getContent(), result.content());
    }

    @Test
    void testToResponseDto_WithNullEnums_ShouldReturnNull() {
        // 创建包含null枚举的实体
        AlertResult entityWithNulls = AlertResult.builder()
                .id(2L)
                .enterpriseId("enterprise-002")
                .planId(200L)
                .configurationId(2L)
                .title("测试告警2")
                .content("测试内容2")
                .involvedKeywords("[]")
                .informationSensitivityType(null)
                .contentCategory(null)
                .sourceType(null)
                .contentType(null)
                .contentMatchType(null)
                .mediaLevel(null)
                .warningLevel(null)
                .source("测试来源")
                .provincial("测试省份")
                .warningTime(LocalDateTime.now())
                .similarArticleCount(0)
                .originalContentId("content_null")
                .extendedAttributes("{}")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 执行转换
        AlertResultResponseDto result = alertResultMapper.toResponseDto(entityWithNulls);

        // 验证结果不为空
        assertNotNull(result);

        // 验证null枚举字段返回null
        assertNull(result.informationSensitivityType(), "null枚举应该返回null");
        assertNull(result.contentCategory(), "null枚举应该返回null");
        assertNull(result.sourceType(), "null枚举应该返回null");
        assertNull(result.contentType(), "null枚举应该返回null");
        assertNull(result.contentMatchType(), "null枚举应该返回null");
        assertNull(result.mediaLevel(), "null枚举应该返回null");
        assertNull(result.warningLevel(), "null枚举应该返回null");

        // 验证其他字段正常
        assertEquals(entityWithNulls.getId(), result.id());
        assertEquals(entityWithNulls.getTitle(), result.title());
    }

    @Test
    void testEnumValueTypes() {
        // 验证各个枚举的value类型
        assertTrue(InformationSensitivityType.SENSITIVE.getValue() instanceof Integer,
                "InformationSensitivityType.getValue() 应该返回Integer类型");

        assertTrue(ContentCategory.ORIGINAL.getValue() instanceof Integer,
                "ContentCategory.getValue() 应该返回Integer类型");

        assertTrue(SourceType.WEIBO.getValue() instanceof String,
                "SourceType.getValue() 应该返回String类型");

        assertTrue(ContentType.TEXT.getValue() instanceof String,
                "ContentType.getValue() 应该返回String类型");

        assertTrue(ContentMatchType.MAIN_TXT.getValue() instanceof String,
                "ContentMatchType.getValue() 应该返回String类型");

        assertTrue(MediaLevel.NATIONAL.getValue() instanceof String,
                "mediaLevels.getValue() 应该返回String类型");

        // WarningLevel使用name()方法
        assertTrue(AlertResult.WarningLevel.SEVERE.name() instanceof String,
                "WarningLevel.name() 应该返回String类型");
    }
}
