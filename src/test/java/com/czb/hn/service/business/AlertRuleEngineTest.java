package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Alert Rule Engine Test
 * 告警规则引擎单元测试
 */
class AlertRuleEngineTest {

    private ObjectMapper objectMapper = new ObjectMapper();
    private AlertRuleEngine alertRuleEngine;

    private AlertConfiguration testConfig;
    private SinaNewsDocument testDocument;

    @BeforeEach
    void setUp() {
        // 初始化 AlertRuleEngine 并注入 ObjectMapper
        alertRuleEngine = new AlertRuleEngine();
        ReflectionTestUtils.setField(alertRuleEngine, "objectMapper", objectMapper);

        // 设置测试配置
        testConfig = new AlertConfiguration();
        testConfig.setId(1L);
        testConfig.setName("测试配置");
        testConfig.setEnterpriseId("enterprise-001");
        testConfig.setAlertKeywords("{\"keywords\":[\"紧急\",\"重要\"],\"description\":\"测试关键词\"}");
        testConfig.setContentSettings(
                "{\"sensitivityTypes\":1,\"sourceTypes\":[\"WEIBO\"],\"contentTypes\":[\"TEXT\"],\"resultDisplay\":\"NORMAL\",\"userTypes\":[\"MEDIA\"],\"weiboVerification\":[\"GOLD_V\"],\"targetedSources\":[\"ALL\"],\"sourceLevel\":[\"NATIONAL\"],\"mediaInfo\":\"ALL\",\"excludeTags\":[],\"involvementMethod\":\"CONTENT\",\"matchMethod\":\"FULL_TEXT\",\"infoType\":\"ALL\",\"contentCategory\":\"ALL\",\"forwardCategory\":\"ALL\",\"sourceRegion\":\"ALL\",\"deduplication\":false,\"imageTextRecognition\":\"ALL\",\"duplicateData\":\"ALL\"}");
        testConfig.setThresholdSettings(
                "{\"conditionRelation\":\"OR\",\"interactionCount\":null,\"fansCount\":null,\"readCount\":null,\"similarArticleCount\":{\"enabled\":true,\"threshold\":1},\"keywordFrequency\":[]}");

        // 设置测试文档
        testDocument = new SinaNewsDocument();
        testDocument.setContentId("doc-001");
        testDocument.setTitle("这是一个紧急通知");
        testDocument.setContent("这是一个紧急通知，关于重要系统的维护工作。请所有用户注意。");
        testDocument.setSource("微博");
        testDocument.setPublishTime(LocalDateTime.now());
        testDocument.setSimilarityNum(5L);
        testDocument.setSensitivityType(1); // SENSITIVE
    }

    @Test
    void testEvaluateRule_ShouldReturnTrue_WhenAllConditionsMatch() throws Exception {
        // 调试信息
        System.out.println("测试文档标题: " + testDocument.getTitle());
        System.out.println("测试文档内容: " + testDocument.getContent());
        System.out.println("相似文章数: " + testDocument.getSimilarityNum());
        System.out.println("关键词配置: " + testConfig.getAlertKeywords());
        System.out.println("阈值配置: " + testConfig.getThresholdSettings());

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果
        assertTrue(result, "规则应该匹配，因为所有条件都满足");
    }

    @Test
    void testEvaluateRule_ShouldReturnFalse_WhenKeywordsDoNotMatch() throws Exception {
        // 修改配置，使用不匹配的关键词
        testConfig.setAlertKeywords("{\"keywords\":[\"不存在的关键词\",\"另一个不存在的词\"],\"description\":\"测试关键词\"}");

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果
        assertFalse(result, "规则不应该匹配，因为关键词不匹配");
    }

    @Test
    void testEvaluateRule_ShouldHandleNullContentSettings() throws Exception {
        // 设置 contentSettings 为 null
        testConfig.setContentSettings(null);

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果 - 应该仍然能正常工作，因为 contentSettings 为 null 时返回 true
        assertTrue(result, "规则应该匹配，即使 contentSettings 为 null");
    }

    @Test
    void testEvaluateRule_ShouldHandleNullThresholdSettings() throws Exception {
        // 设置 thresholdSettings 为 null
        testConfig.setThresholdSettings(null);

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果 - 应该仍然能正常工作，因为 thresholdSettings 为 null 时返回 true
        assertTrue(result, "规则应该匹配，即使 thresholdSettings 为 null");
    }

    @Test
    void testEvaluateRule_ShouldHandleEmptyJsonStrings() throws Exception {
        // 设置空的 JSON 字符串
        testConfig.setContentSettings("");
        testConfig.setThresholdSettings("   ");

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果 - 应该仍然能正常工作，因为空字符串会被解析为 null
        assertTrue(result, "规则应该匹配，即使配置为空字符串");
    }

    @Test
    void testDetermineWarningLevel_ShouldReturnGeneral_WhenLevelSettingsIsNull() throws Exception {
        // 执行测试
        var warningLevel = alertRuleEngine.determineWarningLevel(null, testDocument);

        // 验证结果
        assertEquals(AlertResult.WarningLevel.GENERAL, warningLevel,
                "当 levelSettings 为 null 时应该返回 GENERAL 级别");
    }

    @Test
    void testEvaluateRule_ShouldReturnFalse_WhenThresholdNotMet() throws Exception {
        // 修改配置，设置更高的相似度阈值
        testConfig.setThresholdSettings(
                "{\"conditionRelation\":\"OR\",\"interactionCount\":null,\"fansCount\":null,\"readCount\":null,\"similarArticleCount\":{\"enabled\":true,\"threshold\":10},\"keywordFrequency\":[]}");

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果
        assertFalse(result, "规则不应该匹配，因为相似度阈值不满足");
    }

    @Test
    void testEvaluateRule_ShouldHandleNullDocument() throws Exception {
        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, null);

        // 验证结果
        assertFalse(result, "规则不应该匹配，因为文档为空");
    }

    @Test
    void testEvaluateRule_ShouldHandleNullConfiguration() throws Exception {
        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(null, testDocument);

        // 验证结果
        assertFalse(result, "规则不应该匹配，因为配置为空");
    }

    @Test
    void testEvaluateRule_ShouldHandleInvalidJson() throws Exception {
        // 设置无效的JSON
        testConfig.setAlertKeywords("invalid json");

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果
        assertFalse(result, "规则不应该匹配，因为JSON格式无效");
    }

    @Test
    void testEvaluateRule_ShouldMatchCaseInsensitive() throws Exception {
        // 修改文档内容为大写
        testDocument.setTitle("这是一个IMPORTANT通知");
        testDocument.setContent("这是一个IMPORTANT通知，关于重要系统的维护工作。");

        // 修改关键词为小写
        testConfig.setAlertKeywords("{\"keywords\":[\"important\"],\"description\":\"测试关键词\"}");

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果
        assertTrue(result, "规则应该匹配，因为关键词匹配是大小写不敏感的");
    }

    @Test
    void testEvaluateRule_ShouldMatchPartialKeywords() throws Exception {
        // 修改关键词，只包含部分匹配的词
        testConfig.setAlertKeywords("{\"keywords\":[\"系统\",\"维护\"],\"description\":\"测试关键词\"}");

        // 执行测试
        boolean result = alertRuleEngine.evaluateRule(testConfig, testDocument);

        // 验证结果
        assertTrue(result, "规则应该匹配，因为文档包含部分关键词");
    }
}
