package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import com.czb.hn.jpa.securadar.repository.AlertNotificationQueueRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.service.business.ReceptionRulesEngine;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for integrated alert notification scheduling
 */
@ExtendWith(MockitoExtension.class)
class AlertNotificationSchedulerIntegratedTest {

        @Mock
        private AlertNotificationQueueRepository notificationQueueRepository;

        @Mock
        private AlertResultRepository alertResultRepository;

        @Mock
        private AlertConfigurationConsumerService alertConfigConsumerService;

        @Mock
        private ReceptionRulesEngine receptionRulesEngine;

        @Mock
        private AlertPushService alertPushService;

        @Mock
        private PlanService planService;

        @Mock
        private ObjectMapper objectMapper;

        @InjectMocks
        private AlertNotificationSchedulerImpl scheduler;

        private AlertConfigurationResponseDto testConfiguration;
        private ReceptionSettingsDto receptionSettings;
        private LocalDateTime testTime;

        @BeforeEach
        void setUp() {
                testTime = LocalDateTime.of(2024, 1, 15, 10, 0); // Monday 10:00

                // Create test reception settings
                ReceptionSettingsDto.TimePeriodDto timePeriod = new ReceptionSettingsDto.TimePeriodDto("09:00",
                                "18:00");

                ReceptionSettingsDto.EmailRecipientDto emailRecipient = new ReceptionSettingsDto.EmailRecipientDto(
                                "Admin",
                                "<EMAIL>");
                ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(true,
                                List.of(emailRecipient));

                ReceptionSettingsDto.SmsRecipientDto smsRecipient = new ReceptionSettingsDto.SmsRecipientDto("Admin",
                                "13800138000");
                ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(true,
                                List.of(smsRecipient));

                ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                                emailConfig, smsConfig);

                receptionSettings = new ReceptionSettingsDto(
                                "DAILY", 30, timePeriod, true, receptionMethods, false); // infoPush enabled

                testConfiguration = new AlertConfigurationResponseDto(
                                1L, "Test Configuration", "Test Description", 1L, "enterprise123", true,
                                null, null, null, null, receptionSettings,
                                testTime, testTime, "testUser", "testUser", 1, true, testTime);
        }

        @Test
        void testIntegratedAlertScheduling_WithInfoPushEnabled_FirstTime() {
                // Given - First time scheduling (no previous notifications)
                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.empty());

                List<AlertResult> mockAlerts = Arrays.asList(
                                createMockAlert(1L, testTime.minusMinutes(15)),
                                createMockAlert(2L, testTime.minusMinutes(45)) // This would be from non-reception
                                                                               // period
                );

                when(alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), any(LocalDateTime.class), eq(testTime)))
                                .thenReturn(mockAlerts);

                // Mock dependencies for createBatchNotification
                setupMockDependencies();

                // When
                int result = invokeScheduleAlertNotifications();

                // Then
                assertEquals(1, result); // Should create one batch notification
                verify(alertResultRepository).findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), eq(testTime.minusMinutes(30)), eq(testTime)); // Default interval used
        }

        @Test
        void testIntegratedAlertScheduling_WithInfoPushEnabled_WithHistory() {
                // Given - Has previous notification history
                AlertNotificationQueue lastNotification = AlertNotificationQueue.builder()
                                .id(1L)
                                .configurationId(1L)
                                .scheduledTime(testTime.minusHours(2)) // 2 hours ago
                                .status(NotificationStatus.COMPLETED)
                                .notificationType(NotificationType.ALERT)
                                .build();

                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.of(lastNotification));

                List<AlertResult> mockAlerts = Arrays.asList(
                                createMockAlert(1L, testTime.minusMinutes(15)),
                                createMockAlert(2L, testTime.minusMinutes(90)) // From before last notification
                );

                when(alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), any(LocalDateTime.class), eq(testTime)))
                                .thenReturn(mockAlerts);

                // Mock dependencies for createBatchNotification
                setupMockDependencies();

                // When
                int result = invokeScheduleAlertNotifications();

                // Then
                assertEquals(1, result);
                // Since it's not at period start time, should use regular alert interval
                verify(alertResultRepository).findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), eq(testTime.minusMinutes(30)), eq(testTime)); // Regular interval, not info push
        }

        @Test
        void testIntegratedAlertScheduling_WithInfoPushDisabled() {
                // Given - Info push disabled
                ReceptionSettingsDto settingsWithoutInfoPush = new ReceptionSettingsDto(
                                "DAILY", 30, receptionSettings.receptionPeriod(), false,
                                receptionSettings.receptionMethods(), false);

                testConfiguration = new AlertConfigurationResponseDto(
                                1L, "Test Configuration", "Test Description", 1L, "enterprise123", true,
                                null, null, null, null, settingsWithoutInfoPush,
                                testTime, testTime, "testUser", "testUser", 1, true, testTime);

                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.empty());

                List<AlertResult> mockAlerts = Arrays.asList(createMockAlert(1L, testTime.minusMinutes(15)));

                when(alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), any(LocalDateTime.class), eq(testTime)))
                                .thenReturn(mockAlerts);

                // Mock dependencies for createBatchNotification
                setupMockDependencies();

                // When
                int result = invokeScheduleAlertNotifications();

                // Then
                assertEquals(1, result);
                // Should still query alerts but with ALERT type instead of INFO_PUSH
        }

        @Test
        void testIntegratedAlertScheduling_OutsideReceptionPeriod() {
                // Given - Current time outside reception period
                LocalDateTime outsideTime = LocalDateTime.of(2024, 1, 15, 20, 0); // 20:00, outside 09:00-18:00

                // When
                int result = invokeScheduleAlertNotifications(outsideTime);

                // Then
                assertEquals(0, result); // Should not schedule anything
                verify(alertResultRepository, never()).findUnnotifiedAlertsByConfigurationAndTimeRange(
                                anyLong(), any(LocalDateTime.class), any(LocalDateTime.class));
        }

        @Test
        void testIntegratedAlertScheduling_LongTimeRange_Limited() {
                // Given - Very old last notification (should be limited to 7 days)
                AlertNotificationQueue oldNotification = AlertNotificationQueue.builder()
                                .id(1L)
                                .configurationId(1L)
                                .scheduledTime(testTime.minusDays(30)) // 30 days ago
                                .status(NotificationStatus.COMPLETED)
                                .notificationType(NotificationType.ALERT)
                                .build();

                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.of(oldNotification));

                when(alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), any(LocalDateTime.class), eq(testTime)))
                                .thenReturn(Arrays.asList(createMockAlert(1L, testTime.minusMinutes(15))));

                // Mock dependencies for createBatchNotification
                setupMockDependencies();

                // When
                int result = invokeScheduleAlertNotifications();

                // Then
                assertEquals(1, result);
                // Since it's not at period start time, should use regular alert interval
                verify(alertResultRepository).findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), eq(testTime.minusMinutes(30)), eq(testTime)); // Regular interval, not info push
        }

        private void setupMockDependencies() {
                // Mock alert configuration service
                when(alertConfigConsumerService.getActiveConfigurationById(1L))
                                .thenReturn(Optional.of(testConfiguration));

                // Mock reception rules engine
                ReceptionRulesEngine.RecipientInfo mockRecipient = new ReceptionRulesEngine.RecipientInfo(
                                "Admin", "<EMAIL>", "13800138000", "admin", true, true);
                when(receptionRulesEngine.extractRecipients(any(ReceptionSettingsDto.class)))
                                .thenReturn(Arrays.asList(mockRecipient));

                // Mock object mapper
                try {
                        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
                } catch (Exception e) {
                        // Ignore
                }

                // Mock notification queue save
                AlertNotificationQueue mockNotification = AlertNotificationQueue.builder()
                                .id(1L)
                                .planId(1L)
                                .configurationId(1L)
                                .enterpriseId("enterprise123")
                                .build();
                when(notificationQueueRepository.save(any(AlertNotificationQueue.class)))
                                .thenReturn(mockNotification);

                // Mock alert result save
                when(alertResultRepository.saveAll(any())).thenReturn(Arrays.asList());
        }

        private AlertResult createMockAlert(Long id, LocalDateTime warningTime) {
                return AlertResult.builder()
                                .id(id)
                                .configurationId(1L)
                                .planId(1L) // Add planId to avoid null pointer exception
                                .enterpriseId("enterprise123")
                                .warningTime(warningTime)
                                .title("Test Alert")
                                .content("Test content")
                                .build();
        }

        private int invokeScheduleAlertNotifications() {
                return invokeScheduleAlertNotifications(testTime);
        }

        private int invokeScheduleAlertNotifications(LocalDateTime currentTime) {
                try {
                        // Use reflection to call private method for testing
                        var method = AlertNotificationSchedulerImpl.class.getDeclaredMethod(
                                        "scheduleAlertNotifications", AlertConfigurationResponseDto.class,
                                        LocalDateTime.class);
                        method.setAccessible(true);
                        return (Integer) method.invoke(scheduler, testConfiguration, currentTime);
                } catch (Exception e) {
                        throw new RuntimeException("Failed to invoke scheduleAlertNotifications", e);
                }
        }

        @Test
        void testIntegratedAlertScheduling_InfoPushAtPeriodStart() {
                // Given - Info push enabled and current time is at reception period start
                // (09:02)
                LocalDateTime periodStartTime = LocalDateTime.of(2024, 1, 15, 9, 2); // 09:02, within start window

                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.empty());

                when(alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), any(LocalDateTime.class), eq(periodStartTime)))
                                .thenReturn(Arrays.asList(createMockAlert(1L, periodStartTime.minusMinutes(15))));

                // Mock dependencies for createBatchNotification
                setupMockDependencies();

                // When
                int result = invokeScheduleAlertNotifications(periodStartTime);

                // Then
                assertEquals(1, result);
                // Should trigger info push because it's at period start
        }

        @Test
        void testIntegratedAlertScheduling_InfoPushNotAtPeriodStart() {
                // Given - Info push enabled but current time is not at reception period start
                // (10:00)
                LocalDateTime notPeriodStartTime = LocalDateTime.of(2024, 1, 15, 10, 0); // 10:00, not at start

                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.empty());

                when(alertResultRepository.findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), any(LocalDateTime.class), eq(notPeriodStartTime)))
                                .thenReturn(Arrays.asList(createMockAlert(1L, notPeriodStartTime.minusMinutes(15))));

                // Mock dependencies for createBatchNotification
                setupMockDependencies();

                // When
                int result = invokeScheduleAlertNotifications(notPeriodStartTime);

                // Then
                assertEquals(1, result);
                // Should trigger regular alert, not info push
                verify(alertResultRepository).findUnnotifiedAlertsByConfigurationAndTimeRange(
                                eq(1L), eq(notPeriodStartTime.minusMinutes(30)), eq(notPeriodStartTime)); // Regular
                                                                                                          // interval
        }

        @Test
        void testIntegratedAlertScheduling_IntervalNotSatisfied() {
                // Given - Recent notification within interval
                AlertNotificationQueue recentNotification = AlertNotificationQueue.builder()
                                .id(1L)
                                .configurationId(1L)
                                .scheduledTime(testTime.minusMinutes(15)) // 15 minutes ago, interval is 30 minutes
                                .status(NotificationStatus.COMPLETED)
                                .notificationType(NotificationType.ALERT)
                                .build();

                when(notificationQueueRepository.findLastSuccessfulAlertNotification(1L))
                                .thenReturn(Optional.of(recentNotification));

                // When
                int result = invokeScheduleAlertNotifications();

                // Then
                assertEquals(0, result); // Should not send notification due to interval not satisfied
                verify(alertResultRepository, never()).findUnnotifiedAlertsByConfigurationAndTimeRange(
                                anyLong(), any(LocalDateTime.class), any(LocalDateTime.class));
        }
}
