package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.config.LevelSettingsDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for AlertRuleEngine warning level determination
 */
@ExtendWith(MockitoExtension.class)
class AlertRuleEngineWarningLevelTest {

    @InjectMocks
    private AlertRuleEngine alertRuleEngine;

    private SinaNewsDocument testDocument;

    @BeforeEach
    void setUp() {
        testDocument = new SinaNewsDocument();
        testDocument.setContentId("test-content-123");
        testDocument.setMediaLevel("央级");
        testDocument.setAuthorFollowersCount(5000L);
        testDocument.setLookingCount(10000L);
        testDocument.setSimilarityNum(25L);
        testDocument.setCommentCount(100L);
        testDocument.setForwardCount(50L);
        testDocument.setPraiseCount(200L);
        testDocument.setShareCount(30L);
    }

    @Test
    @DisplayName("Should return null when level settings is null")
    void testNullLevelSettings() {
        AlertResult.WarningLevel result = alertRuleEngine.determineWarningLevel(null, testDocument);
        assertNull(result);
    }

    @Test
    @DisplayName("Should determine warning level based on source level only")
    void testSourceLevelOnly() {
        // Create level settings with only source level enabled
        LevelSettingsDto levelSettings = new LevelSettingsDto(
                true, // sourceLevelEnabled
                Map.of("央级", "SEVERE", "省级", "MODERATE", "地市", "GENERAL"),
                createDisabledThresholds(), // interaction
                createDisabledThresholds(), // fans
                createDisabledThresholds(), // read
                createDisabledThresholds() // similar
        );

        AlertResult.WarningLevel result = alertRuleEngine.determineWarningLevel(levelSettings, testDocument);
        assertEquals(AlertResult.WarningLevel.SEVERE, result);
    }

    @Test
    @DisplayName("Should determine warning level based on multiple conditions and return highest")
    void testMultipleConditionsHighestLevel() {
        // Create level settings with multiple conditions enabled
        LevelSettingsDto levelSettings = new LevelSettingsDto(
                true, // sourceLevelEnabled
                Map.of("央级", "MODERATE"), // source gives MODERATE
                createEnabledThresholds(0L, 99L, 100L, 999L, 1000L, null), // interaction gives SEVERE (380 total)
                createDisabledThresholds(), // fans disabled
                createDisabledThresholds(), // read disabled
                createDisabledThresholds() // similar disabled
        );

        AlertResult.WarningLevel result = alertRuleEngine.determineWarningLevel(levelSettings, testDocument);
        // Should return SEVERE (highest level from interaction count)
        assertEquals(AlertResult.WarningLevel.SEVERE, result);
    }

    @Test
    @DisplayName("Should handle unknown media level gracefully")
    void testUnknownMediaLevel() {
        testDocument.setMediaLevel("未知级别");

        LevelSettingsDto levelSettings = new LevelSettingsDto(
                true, // sourceLevelEnabled
                Map.of("央级", "SEVERE"),
                createDisabledThresholds(),
                createDisabledThresholds(),
                createDisabledThresholds(),
                createDisabledThresholds());

        AlertResult.WarningLevel result = alertRuleEngine.determineWarningLevel(levelSettings, testDocument);
        assertNull(result); // Should return null when no conditions match
    }

    @Test
    @DisplayName("Should handle null document fields gracefully")
    void testNullDocumentFields() {
        testDocument.setAuthorFollowersCount(null);
        testDocument.setLookingCount(null);
        testDocument.setSimilarityNum(null);

        LevelSettingsDto levelSettings = new LevelSettingsDto(
                false, // sourceLevelEnabled
                null,
                createEnabledThresholds(0L, 99L, 100L, 999L, 1000L, null), // interaction
                createEnabledThresholds(0L, 999L, 1000L, 9999L, 10000L, null), // fans
                createEnabledThresholds(0L, 4999L, 5000L, 49999L, 50000L, null), // read
                createEnabledThresholds(0L, 9L, 10L, 49L, 50L, null) // similar
        );

        AlertResult.WarningLevel result = alertRuleEngine.determineWarningLevel(levelSettings, testDocument);
        // Should handle null values as 0 and return SEVERE for interaction (380 total >
        // 1000)
        assertEquals(AlertResult.WarningLevel.SEVERE, result);
    }

    @Test
    @DisplayName("Should return highest level when multiple conditions match")
    void testMultipleConditionsReturnHighest() {
        // Test document has: 央级 (should be MODERATE), interaction 380 (should be
        // SEVERE)
        LevelSettingsDto levelSettings = new LevelSettingsDto(
                true, // sourceLevelEnabled
                Map.of("央级", "MODERATE", "省级", "GENERAL"), // source gives MODERATE
                createEnabledThresholds(0L, 99L, 100L, 999L, 1000L, null), // interaction gives SEVERE
                createDisabledThresholds(), // fans disabled
                createDisabledThresholds(), // read disabled
                createDisabledThresholds() // similar disabled
        );

        AlertResult.WarningLevel result = alertRuleEngine.determineWarningLevel(levelSettings, testDocument);
        // Should return SEVERE (highest between MODERATE and SEVERE)
        assertEquals(AlertResult.WarningLevel.SEVERE, result);
    }

    private LevelSettingsDto.LevelThresholdsDto createDisabledThresholds() {
        return new LevelSettingsDto.LevelThresholdsDto(
                false, // disabled
                null,
                null,
                null);
    }

    private LevelSettingsDto.LevelThresholdsDto createEnabledThresholds(
            Long generalMin, Long generalMax,
            Long moderateMin, Long moderateMax,
            Long severeMin, Long severeMax) {
        return new LevelSettingsDto.LevelThresholdsDto(
                true, // enabled
                new LevelSettingsDto.ThresholdRangeDto(generalMin, generalMax),
                new LevelSettingsDto.ThresholdRangeDto(moderateMin, moderateMax),
                new LevelSettingsDto.ThresholdRangeDto(severeMin, severeMax));
    }
}
