package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertPushDetailCreateDto;
import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.repository.AlertPushDetailRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.ReceptionRulesEngine;
import com.czb.hn.util.SmsUtil;
import com.czb.hn.util.EmailUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AlertPushServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class AlertPushServiceImplTest {

        @Mock
        private AlertPushDetailRepository alertPushDetailRepository;

        @Mock
        private AlertResultRepository alertResultRepository;

        @Mock
        private SmsUtil smsUtil;

        @Mock
        private EmailUtil emailUtil;

        @InjectMocks
        private AlertPushServiceImpl alertPushService;

        private AlertPushDetailCreateDto createDto;
        private AlertPushDetail pushDetail;
        private AlertResult alertResult;

        @BeforeEach
        void setUp() {
                LocalDateTime now = LocalDateTime.now();

                createDto = new AlertPushDetailCreateDto(
                                1L, // alertId
                                1L, // planId
                                1L, // alertConfigSnapshotId
                                "enterprise123", // enterpriseId
                                "<EMAIL>", // accountInfo
                                PushType.EMAIL, // pushType
                                PushStatus.SUCCESS, // pushStatus
                                now, // pushTime
                                null, // errorMessage
                                "邮件发送成功", // pushDetails
                                "admin"); // createdBy

                pushDetail = AlertPushDetail.builder()
                                .id(1L)
                                .planId(1L)
                                .alertConfigSnapshotId(1L)
                                .enterpriseId("enterprise123")
                                .accountInfo("<EMAIL>")
                                .pushType(PushType.EMAIL)
                                .pushStatus(PushStatus.SUCCESS)
                                .pushTime(now)
                                .pushDetails("邮件发送成功")
                                .retryCount(0)
                                .createdBy("admin")
                                .createdAt(now)
                                .updatedAt(now)
                                .build();

                alertResult = AlertResult.builder()
                                .id(1L)
                                .enterpriseId("enterprise123")
                                .title("测试预警")
                                .content("测试预警内容")
                                .warningTime(now)
                                .build();
        }

        @Test
        void testSendEmailNotification() {
                // Given
                doNothing().when(emailUtil).sendEmail(anyString(), anyString(), anyString());

                // When
                var future = alertPushService.sendEmailNotification(
                                "<EMAIL>", "Test Subject", "Test Content");

                // Then
                assertNotNull(future);
                assertTrue(future.join());
                verify(emailUtil, times(1)).sendEmail(eq("<EMAIL>"), eq("Test Subject"), eq("Test Content"));
        }

        @Test
        void testSendSmsNotification() {
                // Given
                doNothing().when(smsUtil).sendSms(anyString(), any(), any());

                // When
                var future = alertPushService.sendSmsNotification("***********", "Test Message");

                // Then
                assertNotNull(future);
                // Note: This is a placeholder implementation that always returns true
                assertTrue(future.join());
                verify(smsUtil, times(1)).sendSms(eq("***********"), any(), any());
        }

        @Test
        void testCreatePushRecordsForAlert_EmailAndSmsEnabled() {
                // Given
                AlertResult alert = AlertResult.builder()
                                .id(1L)
                                .planId(100L)
                                .configurationId(200L)
                                .enterpriseId("enterprise123")
                                .title("测试预警")
                                .content("测试预警内容")
                                .build();

                ReceptionRulesEngine.RecipientInfo recipient = new ReceptionRulesEngine.RecipientInfo(
                                "John Doe",
                                "<EMAIL>",
                                "***********",
                                "john",
                                true, // emailEnabled
                                true // smsEnabled
                );

                AlertPushDetail savedEmailDetail = AlertPushDetail.builder()
                                .id(1L)
                                .planId(100L)
                                .alertConfigSnapshotId(200L)
                                .enterpriseId("enterprise123")
                                .accountInfo("<EMAIL>")
                                .pushType(PushType.EMAIL)
                                .pushStatus(PushStatus.SUCCESS)
                                .build();

                AlertPushDetail savedSmsDetail = AlertPushDetail.builder()
                                .id(2L)
                                .planId(100L)
                                .alertConfigSnapshotId(200L)
                                .enterpriseId("enterprise123")
                                .accountInfo("***********")
                                .pushType(PushType.SMS)
                                .pushStatus(PushStatus.SUCCESS)
                                .build();

                when(alertPushDetailRepository.save(any(AlertPushDetail.class)))
                                .thenReturn(savedEmailDetail)
                                .thenReturn(savedSmsDetail);
                when(alertResultRepository.save(any(AlertResult.class))).thenReturn(alert);

                // When
                alertPushService.createPushRecordsForAlert(alert, recipient);

                // Then
                verify(alertPushDetailRepository, times(2)).save(any(AlertPushDetail.class));
                verify(alertResultRepository, times(2)).save(alert);
        }

        @Test
        void testCreatePushRecordsForAlert_EmailOnlyEnabled() {
                // Given
                AlertResult alert = AlertResult.builder()
                                .id(1L)
                                .planId(100L)
                                .configurationId(200L)
                                .enterpriseId("enterprise123")
                                .title("测试预警")
                                .content("测试预警内容")
                                .build();

                ReceptionRulesEngine.RecipientInfo recipient = new ReceptionRulesEngine.RecipientInfo(
                                "Jane Doe",
                                "<EMAIL>",
                                null,
                                "jane",
                                true, // emailEnabled
                                false // smsEnabled
                );

                AlertPushDetail savedDetail = AlertPushDetail.builder()
                                .id(1L)
                                .planId(100L)
                                .alertConfigSnapshotId(200L)
                                .enterpriseId("enterprise123")
                                .accountInfo("<EMAIL>")
                                .pushType(PushType.EMAIL)
                                .pushStatus(PushStatus.SUCCESS)
                                .build();

                when(alertPushDetailRepository.save(any(AlertPushDetail.class))).thenReturn(savedDetail);
                when(alertResultRepository.save(any(AlertResult.class))).thenReturn(alert);

                // When
                alertPushService.createPushRecordsForAlert(alert, recipient);

                // Then
                verify(alertPushDetailRepository, times(1)).save(any(AlertPushDetail.class));
                verify(alertResultRepository, times(1)).save(alert);
        }

        @Test
        void testCreatePushRecordsForAlert_NoEnabledMethods() {
                // Given
                AlertResult alert = AlertResult.builder()
                                .id(1L)
                                .planId(100L)
                                .configurationId(200L)
                                .enterpriseId("enterprise123")
                                .title("测试预警")
                                .content("测试预警内容")
                                .build();

                ReceptionRulesEngine.RecipientInfo recipient = new ReceptionRulesEngine.RecipientInfo(
                                "Bob Doe",
                                "<EMAIL>",
                                "***********",
                                "bob",
                                false, // emailEnabled
                                false // smsEnabled
                );

                // When
                alertPushService.createPushRecordsForAlert(alert, recipient);

                // Then
                verify(alertPushDetailRepository, never()).save(any(AlertPushDetail.class));
                verify(alertResultRepository, never()).save(any(AlertResult.class));
        }

        @Test
        void testCreatePushRecordsForAlert_InvalidRecipientType() {
                // Given
                AlertResult alert = AlertResult.builder()
                                .id(1L)
                                .planId(100L)
                                .configurationId(200L)
                                .enterpriseId("enterprise123")
                                .title("测试预警")
                                .content("测试预警内容")
                                .build();

                String invalidRecipient = "Invalid recipient type";

                // When & Then
                RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                        alertPushService.createPushRecordsForAlert(alert, invalidRecipient);
                });

                assertTrue(exception.getMessage().contains("Failed to create push records"));
                verify(alertPushDetailRepository, never()).save(any(AlertPushDetail.class));
                verify(alertResultRepository, never()).save(any(AlertResult.class));
        }

        @Test
        void testCreatePushRecordsForAlert_NullAlert() {
                // Given
                ReceptionRulesEngine.RecipientInfo recipient = new ReceptionRulesEngine.RecipientInfo(
                                "John Doe",
                                "<EMAIL>",
                                "***********",
                                "john",
                                true, // emailEnabled
                                true // smsEnabled
                );

                // When & Then
                RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                        alertPushService.createPushRecordsForAlert(null, recipient);
                });

                assertTrue(exception.getMessage().contains("Failed to create push records"));
                verify(alertPushDetailRepository, never()).save(any(AlertPushDetail.class));
                verify(alertResultRepository, never()).save(any(AlertResult.class));
        }

}