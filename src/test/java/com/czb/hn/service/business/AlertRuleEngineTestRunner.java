package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.config.LevelSettingsDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;

import java.util.Map;

/**
 * Simple test runner for AlertRuleEngine warning level determination
 */
public class AlertRuleEngineTestRunner {

        public static void main(String[] args) {
                AlertRuleEngine alertRuleEngine = new AlertRuleEngine();

                // Create test document
                SinaNewsDocument testDocument = new SinaNewsDocument();
                testDocument.setContentId("test-content-123");
                testDocument.setMediaLevel("央级");
                testDocument.setAuthorFollowersCount(5000L);
                testDocument.setLookingCount(10000L);
                testDocument.setSimilarityNum(25L);
                testDocument.setCommentCount(100L);
                testDocument.setForwardCount(50L);
                testDocument.setPraiseCount(200L);
                testDocument.setShareCount(30L);
                // Set total interaction count (sum of individual interactions)
                testDocument.setInteractionCount(380L); // 100 + 50 + 200 + 30

                System.out.println("=== AlertRuleEngine Warning Level Test ===");

                // Test 1: Null level settings
                System.out.println("\nTest 1: Null level settings");
                AlertResult.WarningLevel result1 = alertRuleEngine.determineWarningLevel(null, testDocument);
                System.out.println("Result: " + result1 + " (Expected: null)");

                // Test 2: Source level only
                System.out.println("\nTest 2: Source level only (央级 -> SEVERE)");
                LevelSettingsDto levelSettings2 = new LevelSettingsDto(
                                true, // sourceLevelEnabled
                                Map.of("央级", "SEVERE", "省级", "MODERATE", "地市", "GENERAL"),
                                createDisabledThresholds(), // interaction
                                createDisabledThresholds(), // fans
                                createDisabledThresholds(), // read
                                createDisabledThresholds() // similar
                );
                AlertResult.WarningLevel result2 = alertRuleEngine.determineWarningLevel(levelSettings2, testDocument);
                System.out.println("Result: " + result2 + " (Expected: SEVERE)");

                // Test 3: Multiple conditions - should return highest
                System.out.println("\nTest 3: Multiple conditions (央级=GENERAL, interaction=SEVERE)");
                System.out.println("Document interaction count: " + testDocument.getInteractionCount());
                LevelSettingsDto levelSettings3 = new LevelSettingsDto(
                                true, // sourceLevelEnabled
                                Map.of("央级", "GENERAL"), // source gives GENERAL
                                createEnabledThresholds(0L, 99L, 100L, 299L, 300L, null), // interaction gives SEVERE
                                                                                          // (380 > 300)
                                createDisabledThresholds(), // fans disabled
                                createDisabledThresholds(), // read disabled
                                createDisabledThresholds() // similar disabled
                );
                System.out.println("Interaction thresholds: general(0-99), moderate(100-299), severe(300+)");
                System.out.println("380 should be in severe range (300+)");
                AlertResult.WarningLevel result3 = alertRuleEngine.determineWarningLevel(levelSettings3, testDocument);
                System.out.println("Result: " + result3 + " (Expected: SEVERE - highest between GENERAL and SEVERE)");

                // Test 4: Unknown media level
                System.out.println("\nTest 4: Unknown media level");
                testDocument.setMediaLevel("未知级别");
                LevelSettingsDto levelSettings4 = new LevelSettingsDto(
                                true, // sourceLevelEnabled
                                Map.of("央级", "SEVERE"),
                                createDisabledThresholds(),
                                createDisabledThresholds(),
                                createDisabledThresholds(),
                                createDisabledThresholds());
                AlertResult.WarningLevel result4 = alertRuleEngine.determineWarningLevel(levelSettings4, testDocument);
                System.out.println("Result: " + result4 + " (Expected: null)");

                System.out.println("\n=== Test completed ===");
        }

        private static LevelSettingsDto.LevelThresholdsDto createDisabledThresholds() {
                return new LevelSettingsDto.LevelThresholdsDto(
                                false, // disabled
                                null,
                                null,
                                null);
        }

        private static LevelSettingsDto.LevelThresholdsDto createEnabledThresholds(
                        Long generalMin, Long generalMax,
                        Long moderateMin, Long moderateMax,
                        Long severeMin, Long severeMax) {
                return new LevelSettingsDto.LevelThresholdsDto(
                                true, // enabled
                                new LevelSettingsDto.ThresholdRangeDto(generalMin, generalMax),
                                new LevelSettingsDto.ThresholdRangeDto(moderateMin, moderateMax),
                                new LevelSettingsDto.ThresholdRangeDto(severeMin, severeMax));
        }
}
