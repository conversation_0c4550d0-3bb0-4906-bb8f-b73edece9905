package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.*;
import com.czb.hn.dto.alert.config.*;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertConfigurationSnapshot;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationRepository;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationSnapshotRepository;
import com.czb.hn.service.business.AlertConfigurationService;
import com.czb.hn.util.AlertConfigurationMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive tests for sentiment monitoring alert configuration
 * functionality
 * Focuses on alert creation, triggering conditions, notifications, and edge
 * cases
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Sentiment Monitoring Alert Configuration Tests")
public class AlertConfigurationSentimentMonitoringTest {

        @Mock
        private AlertConfigurationRepository alertConfigurationRepository;

        @Mock
        private AlertConfigurationSnapshotRepository snapshotRepository;

        @Mock
        private AlertConfigurationMapper configurationMapper;

        @Mock
        private ObjectMapper objectMapper;

        @InjectMocks
        private AlertConfigurationServiceImpl alertConfigurationService;

        private AlertConfiguration testConfiguration;
        private AlertConfigurationCreateDto sentimentCreateDto;
        private AlertConfigurationResponseDto sentimentResponseDto;
        private AlertConfigurationSnapshot testSnapshot;

        @BeforeEach
        void setUp() {
                setupTestConfiguration();
                setupTestSnapshot();
                setupSentimentMonitoringDto();
                setupSentimentResponseDto();
        }

        @Nested
        @DisplayName("Alert Creation and Configuration Tests")
        class AlertCreationTests {

                @Test
                @DisplayName("Should create sentiment monitoring alert with critical keywords")
                void createSentimentAlert_WithCriticalKeywords_Success() {
                        // Given
                        when(alertConfigurationRepository.existsByNameAndEnterpriseId(anyString(), anyString()))
                                        .thenReturn(false);
                        when(configurationMapper.toEntity(any(AlertConfigurationCreateDto.class)))
                                        .thenReturn(testConfiguration);
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenReturn(testConfiguration);
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(sentimentResponseDto);
                        when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                        .thenReturn("{\"test\":\"snapshot\"}");
                        when(snapshotRepository.save(any(AlertConfigurationSnapshot.class)))
                                        .thenReturn(testSnapshot);

                        // When
                        AlertConfigurationResponseDto result = alertConfigurationService
                                        .createConfiguration(sentimentCreateDto);

                        // Then
                        assertNotNull(result);
                        assertEquals("Sentiment Crisis Alert", result.name());
                        assertEquals("enterprise-sentiment", result.enterpriseId());
                        assertTrue(result.enabled());

                        // Verify sentiment-specific keywords are configured
                        assertNotNull(result.alertKeywords());
                        assertTrue(result.alertKeywords().keywords().contains("crisis"));
                        assertTrue(result.alertKeywords().keywords().contains("scandal"));

                        verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                        verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
                }

                @Test
                @DisplayName("Should create alert with complex threshold conditions for sentiment monitoring")
                void createSentimentAlert_WithComplexThresholds_Success() {
                        // Given
                        AlertConfigurationCreateDto complexThresholdDto = createComplexThresholdDto();

                        // Create a response DTO with AND threshold relation
                        AlertConfigurationResponseDto complexThresholdResponse = new AlertConfigurationResponseDto(
                                        1L, "Complex Threshold Alert", "Alert with complex threshold conditions", 1L,
                                        "enterprise-sentiment", true, sentimentCreateDto.alertKeywords(),
                                        sentimentCreateDto.contentSettings(), complexThresholdDto.thresholdSettings(),
                                        sentimentCreateDto.levelSettings(), sentimentCreateDto.receptionSettings(),
                                        LocalDateTime.now(), LocalDateTime.now(), "admin", "admin", 1, true,
                                        LocalDateTime.now());

                        when(alertConfigurationRepository.existsByNameAndEnterpriseId(anyString(), anyString()))
                                        .thenReturn(false);
                        when(configurationMapper.toEntity(any(AlertConfigurationCreateDto.class)))
                                        .thenReturn(testConfiguration);
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenReturn(testConfiguration);
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(complexThresholdResponse);
                        when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                        .thenReturn("{\"test\":\"snapshot\"}");
                        when(snapshotRepository.save(any(AlertConfigurationSnapshot.class)))
                                        .thenReturn(testSnapshot);

                        // When
                        AlertConfigurationResponseDto result = alertConfigurationService
                                        .createConfiguration(complexThresholdDto);

                        // Then
                        assertNotNull(result);
                        assertNotNull(result.thresholdSettings());
                        assertEquals("AND", result.thresholdSettings().conditionRelation());

                        verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                }

                @Test
                @DisplayName("Should fail to create alert with duplicate name in same enterprise")
                void createSentimentAlert_DuplicateName_ThrowsException() {
                        // Given
                        when(alertConfigurationRepository.existsByNameAndEnterpriseId(anyString(), anyString()))
                                        .thenReturn(true);

                        // When & Then
                        RuntimeException exception = assertThrows(RuntimeException.class,
                                        () -> alertConfigurationService.createConfiguration(sentimentCreateDto));
                        assertTrue(exception.getMessage()
                                        .contains("Configuration name already exists for this enterprise") ||
                                        exception.getMessage().contains("Failed to create alert configuration"));

                        verify(alertConfigurationRepository, never()).save(any());
                        verify(snapshotRepository, never()).save(any());
                }
        }

        @Nested
        @DisplayName("Alert Triggering Conditions Tests")
        class AlertTriggeringTests {

                @Test
                @DisplayName("Should enable alert configuration for sentiment monitoring")
                void toggleSentimentAlert_Enable_Success() {
                        // Given
                        testConfiguration.setEnabled(false);
                        when(alertConfigurationRepository.findActiveById(1L))
                                        .thenReturn(Optional.of(testConfiguration));
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenReturn(testConfiguration);
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(sentimentResponseDto);
                        when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                        .thenReturn("{\"test\":\"snapshot\"}");
                        when(snapshotRepository.save(any(AlertConfigurationSnapshot.class)))
                                        .thenReturn(testSnapshot);

                        // When
                        AlertConfigurationResponseDto result = alertConfigurationService.toggleConfiguration(
                                        1L, true, "sentimentAnalyst", "Enable for crisis monitoring");

                        // Then
                        assertNotNull(result);
                        assertTrue(result.enabled());
                        verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                        verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
                }

                @Test
                @DisplayName("Should disable alert configuration during maintenance")
                void toggleSentimentAlert_Disable_Success() {
                        // Given
                        testConfiguration.setEnabled(true);
                        when(alertConfigurationRepository.findActiveById(1L))
                                        .thenReturn(Optional.of(testConfiguration));
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenReturn(testConfiguration);

                        AlertConfigurationResponseDto disabledResponse = new AlertConfigurationResponseDto(
                                        1L, "Sentiment Crisis Alert", "Monitor sentiment crisis", 1L,
                                        "enterprise-sentiment",
                                        false, null, null, null, null, null,
                                        LocalDateTime.now(), LocalDateTime.now(), "admin", "sentimentAnalyst", 2, true,
                                        LocalDateTime.now());
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(disabledResponse);
                        when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                        .thenReturn("{\"test\":\"snapshot\"}");
                        when(snapshotRepository.save(any(AlertConfigurationSnapshot.class)))
                                        .thenReturn(testSnapshot);

                        // When
                        AlertConfigurationResponseDto result = alertConfigurationService.toggleConfiguration(
                                        1L, false, "sentimentAnalyst", "Disable for system maintenance");

                        // Then
                        assertNotNull(result);
                        assertFalse(result.enabled());
                        verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                        verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
                }

                @Test
                @DisplayName("Should fail to toggle non-existent configuration")
                void toggleSentimentAlert_NotFound_ThrowsException() {
                        // Given
                        when(alertConfigurationRepository.findActiveById(999L)).thenReturn(Optional.empty());

                        // When & Then
                        RuntimeException exception = assertThrows(RuntimeException.class,
                                        () -> alertConfigurationService.toggleConfiguration(999L, true, "user",
                                                        "reason"));
                        assertTrue(exception.getMessage().contains("Alert configuration not found with ID: 999") ||
                                        exception.getMessage().contains("Failed to toggle alert configuration"));

                        verify(alertConfigurationRepository, never()).save(any());
                }
        }

        @Nested
        @DisplayName("Alert Notification Mechanism Tests")
        class AlertNotificationTests {

                @Test
                @DisplayName("Should retrieve enabled configurations for sentiment monitoring")
                void getEnabledConfigurations_ForSentimentMonitoring_Success() {
                        // Given
                        List<AlertConfiguration> enabledConfigs = Arrays.asList(testConfiguration);
                        when(alertConfigurationRepository.findEnabledByEnterpriseId("enterprise-sentiment"))
                                        .thenReturn(enabledConfigs);
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(sentimentResponseDto);

                        // When
                        List<AlertConfigurationResponseDto> result = alertConfigurationService
                                        .getEnabledConfigurationsByEnterpriseId("enterprise-sentiment");

                        // Then
                        assertNotNull(result);
                        assertEquals(1, result.size());
                        assertTrue(result.get(0).enabled());
                        assertEquals("Sentiment Crisis Alert", result.get(0).name());
                }

                @Test
                @DisplayName("Should search configurations by sentiment keywords")
                void searchConfigurations_BySentimentKeywords_Success() {
                        // Given
                        Pageable pageable = PageRequest.of(0, 10);
                        Page<AlertConfiguration> configPage = new PageImpl<>(Arrays.asList(testConfiguration));
                        when(alertConfigurationRepository.findByNameContaining(anyString(), any(Pageable.class)))
                                        .thenReturn(configPage);
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(sentimentResponseDto);

                        // When
                        Page<AlertConfigurationResponseDto> result = alertConfigurationService
                                        .searchConfigurationsByName(
                                                        "sentiment", pageable);

                        // Then
                        assertNotNull(result);
                        assertEquals(1, result.getTotalElements());
                        assertEquals("Sentiment Crisis Alert", result.getContent().get(0).name());
                }
        }

        @Nested
        @DisplayName("Edge Cases and Error Scenarios Tests")
        class EdgeCasesAndErrorTests {

                @Test
                @DisplayName("Should handle null keyword list gracefully")
                void createSentimentAlert_WithNullKeywords_ThrowsException() {
                        // When & Then - Exception should be thrown during DTO construction
                        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                        () -> createInvalidKeywordsDto());
                        assertTrue(exception.getMessage().contains("Alert keywords configuration cannot be null"));
                }

                @Test
                @DisplayName("Should handle invalid threshold values")
                void createSentimentAlert_WithInvalidThresholds_ThrowsException() {
                        // When & Then - Exception should be thrown during DTO construction
                        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                        () -> createInvalidThresholdDto());
                        assertTrue(exception.getMessage().contains("threshold") ||
                                        exception.getMessage().contains("condition") ||
                                        exception.getMessage().contains("non-negative"));
                }

                @Test
                @DisplayName("Should handle configuration update with version conflict")
                void updateSentimentAlert_WithVersionConflict_Success() {
                        // Given
                        AlertConfigurationUpdateDto updateDto = new AlertConfigurationUpdateDto(
                                        "Updated Sentiment Alert",
                                        "Updated description for sentiment monitoring",
                                        null, true, null, null, null, null, null,
                                        "sentimentAnalyst",
                                        "Updated for better monitoring");

                        when(alertConfigurationRepository.findActiveById(1L))
                                        .thenReturn(Optional.of(testConfiguration));
                        when(alertConfigurationRepository.existsByNameAndEnterpriseIdExcludingId(anyString(),
                                        anyString(), anyLong()))
                                        .thenReturn(false);
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenReturn(testConfiguration);
                        when(configurationMapper.toResponseDto(any(AlertConfiguration.class)))
                                        .thenReturn(sentimentResponseDto);
                        when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                        .thenReturn("{\"test\":\"snapshot\"}");
                        when(snapshotRepository.save(any(AlertConfigurationSnapshot.class)))
                                        .thenReturn(testSnapshot);

                        // When
                        AlertConfigurationResponseDto result = alertConfigurationService.updateConfiguration(1L,
                                        updateDto);

                        // Then
                        assertNotNull(result);
                        verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                        verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
                }

                @Test
                @DisplayName("Should handle soft delete of sentiment configuration")
                void deleteSentimentAlert_SoftDelete_Success() {
                        // Given
                        when(alertConfigurationRepository.findActiveById(1L))
                                        .thenReturn(Optional.of(testConfiguration));
                        when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                        .thenReturn("{\"test\":\"snapshot\"}");
                        when(snapshotRepository.save(any(AlertConfigurationSnapshot.class)))
                                        .thenReturn(testSnapshot);

                        // When
                        assertDoesNotThrow(() -> alertConfigurationService.deleteConfiguration(
                                        1L, "sentimentAnalyst", "Decommissioning old sentiment monitoring"));

                        // Then
                        verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
                        verify(alertConfigurationRepository).softDeleteById(1L, "sentimentAnalyst");
                }

                @Test
                @DisplayName("Should handle concurrent access to configuration")
                void updateSentimentAlert_ConcurrentAccess_HandlesGracefully() {
                        // Given
                        AlertConfigurationUpdateDto updateDto = new AlertConfigurationUpdateDto(
                                        "Concurrent Update Test",
                                        null, null, null, null, null, null, null, null,
                                        "user1", "Concurrent update test");

                        when(alertConfigurationRepository.findActiveById(1L))
                                        .thenReturn(Optional.of(testConfiguration));
                        when(alertConfigurationRepository.existsByNameAndEnterpriseIdExcludingId(anyString(),
                                        anyString(), anyLong()))
                                        .thenReturn(false);
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenThrow(new RuntimeException("Optimistic locking failure"))
                                        .thenReturn(testConfiguration);

                        // When & Then
                        RuntimeException exception = assertThrows(RuntimeException.class,
                                        () -> alertConfigurationService.updateConfiguration(1L, updateDto));
                        assertTrue(exception.getMessage().contains("Failed to update alert configuration"));
                }

                @Test
                @DisplayName("Should validate enterprise access permissions")
                void getSentimentConfigurations_WrongEnterprise_ReturnsEmpty() {
                        // Given
                        when(alertConfigurationRepository.findByEnterpriseId("wrong-enterprise"))
                                        .thenReturn(List.of());

                        // When
                        List<AlertConfigurationResponseDto> result = alertConfigurationService
                                        .getConfigurationsByEnterpriseId("wrong-enterprise");

                        // Then
                        assertNotNull(result);
                        assertTrue(result.isEmpty());
                }

                @Test
                @DisplayName("Should handle database connection failures gracefully")
                void createSentimentAlert_DatabaseFailure_ThrowsException() {
                        // Given
                        when(alertConfigurationRepository.existsByNameAndEnterpriseId(anyString(), anyString()))
                                        .thenReturn(false);
                        when(configurationMapper.toEntity(any(AlertConfigurationCreateDto.class)))
                                        .thenReturn(testConfiguration);
                        when(alertConfigurationRepository.save(any(AlertConfiguration.class)))
                                        .thenThrow(new RuntimeException("Database connection failed"));

                        // When & Then
                        RuntimeException exception = assertThrows(RuntimeException.class,
                                        () -> alertConfigurationService.createConfiguration(sentimentCreateDto));
                        assertTrue(exception.getMessage().contains("Failed to create alert configuration"));
                }

                @Test
                @DisplayName("Should validate configuration name length limits")
                void createSentimentAlert_LongName_ThrowsException() {
                        // When & Then - Exception should be thrown during DTO construction
                        String longName = "A".repeat(256); // Exceeds 255 character limit
                        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                        () -> new AlertConfigurationCreateDto(
                                                        longName,
                                                        sentimentCreateDto.description(),
                                                        sentimentCreateDto.planId(),
                                                        sentimentCreateDto.enterpriseId(),
                                                        sentimentCreateDto.enabled(),
                                                        sentimentCreateDto.alertKeywords(),
                                                        sentimentCreateDto.contentSettings(),
                                                        sentimentCreateDto.thresholdSettings(),
                                                        sentimentCreateDto.levelSettings(),
                                                        sentimentCreateDto.receptionSettings(),
                                                        sentimentCreateDto.createdBy(),
                                                        sentimentCreateDto.changeReason()));
                        assertTrue(exception.getMessage().contains("cannot exceed 255 characters"));
                }

                @Test
                @DisplayName("Should handle statistics calculation for sentiment monitoring")
                void getConfigurationStatistics_ForSentimentEnterprise_Success() {
                        // Given
                        when(alertConfigurationRepository.countByEnterpriseId("enterprise-sentiment")).thenReturn(5L);
                        when(alertConfigurationRepository.countEnabledByEnterpriseId("enterprise-sentiment"))
                                        .thenReturn(3L);
                        when(alertConfigurationRepository.findByEnterpriseId("enterprise-sentiment"))
                                        .thenReturn(Arrays.asList(testConfiguration));
                        when(snapshotRepository.countByConfigurationId(1L)).thenReturn(10L);

                        // When
                        AlertConfigurationService.ConfigurationStatisticsDto result = alertConfigurationService
                                        .getConfigurationStatistics("enterprise-sentiment");

                        // Then
                        assertNotNull(result);
                        assertEquals(5L, result.totalConfigurations());
                        assertEquals(3L, result.enabledConfigurations());
                        assertEquals(2L, result.disabledConfigurations());
                        assertEquals(10L, result.totalSnapshots());
                }

                private AlertConfigurationCreateDto createInvalidKeywordsDto() {
                        return new AlertConfigurationCreateDto(
                                        "Invalid Keywords Alert",
                                        "Test invalid keywords",
                                        1L,
                                        "enterprise-sentiment",
                                        true,
                                        null, // Invalid: null keywords
                                        sentimentCreateDto.contentSettings(),
                                        sentimentCreateDto.thresholdSettings(),
                                        sentimentCreateDto.levelSettings(),
                                        sentimentCreateDto.receptionSettings(),
                                        "admin",
                                        "Test invalid keywords");
                }

                private AlertConfigurationCreateDto createInvalidThresholdDto() {
                        ThresholdSettingsDto.ThresholdConditionDto invalidThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                        true, -100L); // Invalid: negative threshold

                        ThresholdSettingsDto invalidThresholds = new ThresholdSettingsDto(
                                        "INVALID", invalidThreshold, null, null, null, List.of() // Invalid: wrong
                                                                                                 // relation type
                        );

                        return new AlertConfigurationCreateDto(
                                        "Invalid Threshold Alert",
                                        "Test invalid thresholds",
                                        1L,
                                        "enterprise-sentiment",
                                        true,
                                        sentimentCreateDto.alertKeywords(),
                                        sentimentCreateDto.contentSettings(),
                                        invalidThresholds,
                                        sentimentCreateDto.levelSettings(),
                                        sentimentCreateDto.receptionSettings(),
                                        "admin",
                                        "Test invalid thresholds");
                }
        }

        // Helper methods for test data setup
        private void setupTestSnapshot() {
                testSnapshot = new AlertConfigurationSnapshot();
                testSnapshot.setId(1L);
                testSnapshot.setConfigurationId(1L);
                testSnapshot.setVersionNumber(1);
                testSnapshot.setSnapshotData("{\"test\":\"snapshot\"}");
                testSnapshot.setCreatedAt(LocalDateTime.now());
                testSnapshot.setCreatedBy("admin");
                testSnapshot.setChangeReason("Test snapshot");
                testSnapshot.setIsActive(true);
                testSnapshot.setOperationType("CREATE");
        }

        private void setupTestConfiguration() {
                testConfiguration = new AlertConfiguration();
                testConfiguration.setId(1L);
                testConfiguration.setName("Sentiment Crisis Alert");
                testConfiguration.setDescription("Monitor sentiment crisis situations");
                testConfiguration.setPlanId(1L);
                testConfiguration.setEnterpriseId("enterprise-sentiment");
                testConfiguration.setEnabled(true);
                testConfiguration.setCurrentVersion(1);
                testConfiguration.setIsActive(true);
                testConfiguration.setCreatedAt(LocalDateTime.now());
                testConfiguration.setUpdatedAt(LocalDateTime.now());
                testConfiguration.setCreatedBy("admin");
                testConfiguration.setUpdatedBy("admin");
        }

        private void setupSentimentMonitoringDto() {
                AlertKeywordsDto sentimentKeywords = new AlertKeywordsDto(
                                List.of("crisis", "scandal", "controversy", "negative sentiment", "public outrage"),
                                "Critical sentiment monitoring keywords");

                ContentSettingsDto sentimentContentSettings = new ContentSettingsDto(
                                "1", // sensitivityTypes (SENSITIVE)
                                List.of("wb", "wx", "wz"), // sourceTypes (WEIBO, WECHAT, WEBSITES)
                                List.of("1", "2"), // contentTypes (TEXT, IMAGE)
                                "1", // resultDisplay (NORMAL)
                                List.of("600", "1"), // weiboVerification (GOLD_V, BLUE_V)
                                List.of("央级", "省级"), // sourceLevel (NATIONAL, PROVINCIAL)
                                "1" // contentCategory
                );

                ThresholdSettingsDto.ThresholdConditionDto interactionThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 500L);
                ThresholdSettingsDto.ThresholdConditionDto fansThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 10000L);

                ThresholdSettingsDto sentimentThresholds = new ThresholdSettingsDto(
                                "OR", interactionThreshold, fansThreshold, null, null, List.of());

                LevelSettingsDto.ThresholdRangeDto generalRange = new LevelSettingsDto.ThresholdRangeDto(0L, 999L);
                LevelSettingsDto.ThresholdRangeDto mediumRange = new LevelSettingsDto.ThresholdRangeDto(1000L, 4999L);
                LevelSettingsDto.ThresholdRangeDto severeRange = new LevelSettingsDto.ThresholdRangeDto(5000L, null);

                LevelSettingsDto.LevelThresholdsDto interactionLevels = new LevelSettingsDto.LevelThresholdsDto(
                                generalRange, mediumRange, severeRange);

                LevelSettingsDto sentimentLevels = new LevelSettingsDto(
                                null, interactionLevels, null, null, null);

                ReceptionSettingsDto.TimePeriodDto timePeriod = new ReceptionSettingsDto.TimePeriodDto("00:00",
                                "24:00");
                ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(
                                true, List.of(new ReceptionSettingsDto.EmailRecipientDto("Crisis Team",
                                                "<EMAIL>")));
                ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(
                                true,
                                List.of(new ReceptionSettingsDto.SmsRecipientDto("Crisis Manager", "13800138000")));
                ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                                emailConfig, smsConfig);

                ReceptionSettingsDto sentimentReception = new ReceptionSettingsDto(
                                "DAILY", 30, timePeriod, true, receptionMethods, true);

                sentimentCreateDto = new AlertConfigurationCreateDto(
                                "Sentiment Crisis Alert",
                                "Monitor sentiment crisis situations",
                                1L,
                                "enterprise-sentiment",
                                true,
                                sentimentKeywords,
                                sentimentContentSettings,
                                sentimentThresholds,
                                sentimentLevels,
                                sentimentReception,
                                "admin",
                                "Initial sentiment monitoring setup");
        }

        private void setupSentimentResponseDto() {
                sentimentResponseDto = new AlertConfigurationResponseDto(
                                1L,
                                "Sentiment Crisis Alert",
                                "Monitor sentiment crisis situations",
                                1L,
                                "enterprise-sentiment",
                                true,
                                sentimentCreateDto.alertKeywords(),
                                sentimentCreateDto.contentSettings(),
                                sentimentCreateDto.thresholdSettings(),
                                sentimentCreateDto.levelSettings(),
                                sentimentCreateDto.receptionSettings(),
                                LocalDateTime.now(),
                                LocalDateTime.now(),
                                "admin",
                                "admin",
                                1,
                                true,
                                LocalDateTime.now());
        }

        private AlertConfigurationCreateDto createComplexThresholdDto() {
                ThresholdSettingsDto.ThresholdConditionDto interactionThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 1000L);
                ThresholdSettingsDto.ThresholdConditionDto fansThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 50000L);
                ThresholdSettingsDto.ThresholdConditionDto readThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 100000L);

                ThresholdSettingsDto complexThresholds = new ThresholdSettingsDto(
                                "AND", interactionThreshold, fansThreshold, readThreshold, null, List.of());

                return new AlertConfigurationCreateDto(
                                "Complex Threshold Alert",
                                "Alert with complex threshold conditions",
                                1L,
                                "enterprise-sentiment",
                                true,
                                sentimentCreateDto.alertKeywords(),
                                sentimentCreateDto.contentSettings(),
                                complexThresholds,
                                sentimentCreateDto.levelSettings(),
                                sentimentCreateDto.receptionSettings(),
                                "admin",
                                "Complex threshold setup");
        }
}
