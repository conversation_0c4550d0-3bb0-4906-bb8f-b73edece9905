package com.czb.hn.util;

import com.czb.hn.dto.recipients.RecipientsCreateDto;
import com.czb.hn.dto.recipients.RecipientsResponseDto;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.entity.Recipients;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Component
@Slf4j
public class RecipientsListMapper {

    private static final Logger logger = LoggerFactory.getLogger(RecipientsListMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    public List<RecipientsResponseDto> toResponseDto(List<Recipients> entityList) {
        try {
            return entityList.stream()
                    .map(entity -> new RecipientsResponseDto(
                            entity.getId(),
                            entity.getName(),
                            entity.getAddress()
                    ))
                    .toList();
        } catch (Exception e) {
            logger.error("Error converting Entity to ResponseDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert Entity to ResponseDto", e);
        }
    }

    public Recipients toCreateEntity(RecipientsCreateDto createDto) {
        try {
            Recipients entity = new Recipients();
            entity.setPlanId(createDto.planId());
            entity.setSystemType(createDto.systemType());
            entity.setReceiveType(PushType.fromString(createDto.receiveType()));
            entity.setName(createDto.name());
            entity.setAddress(createDto.address());
            return entity;
        } catch (Exception e) {
            logger.error("Error converting CreateDto to Entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert CreateDto to Entity", e);
        }
    }
}
