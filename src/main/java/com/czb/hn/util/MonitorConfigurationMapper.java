package com.czb.hn.util;

import com.czb.hn.dto.monitor.MonitorConfigurationCreateDto;
import com.czb.hn.dto.monitor.MonitorConfigurationResponseDto;
import com.czb.hn.dto.monitor.MonitorConfigurationUpdateDto;
import com.czb.hn.jpa.securadar.entity.MonitorConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class MonitorConfigurationMapper {

    private static final Logger logger = LoggerFactory.getLogger(MonitorConfigurationMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    public MonitorConfiguration toEntity(MonitorConfigurationCreateDto createDto) {
        try {
            MonitorConfiguration entity = new MonitorConfiguration();
            entity.setPlanId(createDto.planId());
            entity.setTime(createDto.time());
            entity.setStartTime(createDto.startTime());
            entity.setEndTime(createDto.endTime());
            entity.setSortRule(createDto.sortRule());
            entity.setSensitivityType(createDto.sensitivityType());
            entity.setSimilarityDisplayRule(createDto.similarityDisplayRule());
            entity.setMatchMethod(createDto.matchMethod());
            List<String> mediaTypes = createDto.mediaTypes();
            entity.setMediaType(StringUtils.join(mediaTypes, ","));
            List<String> mediaTypeSeconds = createDto.mediaTypeSeconds();
            entity.setMediaTypeSecond(StringUtils.join(mediaTypeSeconds, ","));
            List<Integer> contentTypes = createDto.contentTypes();
            entity.setContentType(StringUtils.join(contentTypes, ","));
            List<Integer> original = createDto.isOriginal();
            entity.setIsOriginal(StringUtils.join(original, ","));
            entity.setImageTextMode(createDto.imageTextMode());
            List<String> secondTrades = createDto.secondTrades();
            entity.setSecondTrades(StringUtils.join(secondTrades, ","));
            entity.setAuthorFollowersCountMin(createDto.authorFollowersCountMin());
            entity.setAuthorFollowersCountMax(createDto.authorFollowersCountMax());
            List<String> mediaLevels = createDto.mediaLevels();
            entity.setMediaLevel(StringUtils.join(mediaLevels, ","));
            return entity;
        } catch (Exception e) {
            logger.error("Error converting CreateDto to Entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert CreateDto to Entity", e);
        }
    }

    public MonitorConfigurationResponseDto toResponseDto(MonitorConfiguration entity) {
        try {
            String startTime;
            String endTime;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (entity.getTime() == 1) {
                startTime = LocalDateTime.now().with(LocalTime.MIN).format(formatter);
                endTime = LocalDateTime.now().format(formatter);
            } else if (entity.getTime() == 2) {
                startTime = LocalDateTime.now().minusDays(1).format(formatter);
                endTime = LocalDateTime.now().format(formatter);
            } else if (entity.getTime() == 3) {
                startTime = LocalDateTime.now().minusDays(7).format(formatter);
                endTime = LocalDateTime.now().format(formatter);
            } else {
                startTime = entity.getStartTime();
                endTime = entity.getEndTime();
            }

            String mediaType = entity.getMediaType();
            String mediaTypeSecond = entity.getMediaTypeSecond();
            String mediaTypeThird = entity.getMediaTypeThird();
            String contentType = entity.getContentType();
            String isOriginal = entity.getIsOriginal();
            Integer imageTextMode = entity.getImageTextMode();
            String secondTrades = entity.getSecondTrades();
            String mediaLevel = entity.getMediaLevel();

            MonitorConfigurationResponseDto responseDto = new MonitorConfigurationResponseDto(
                    entity.getId(),
                    entity.getPlanId(),
                    entity.getTime(),
                    startTime,
                    endTime,
                    entity.getSortRule(),
                    entity.getSensitivityType(),
                    entity.getSimilarityDisplayRule(),
                    entity.getMatchMethod(),
                    Arrays.stream(StringUtils.split(mediaType != null ? mediaType : "", ",")).toList(),
                    Arrays.stream(StringUtils.split(mediaTypeSecond != null ? mediaTypeSecond : "", ",")).toList(),
                    Arrays.stream(StringUtils.split(mediaTypeThird != null ? mediaTypeThird : "", ",")).toList(),
                    Arrays.stream(StringUtils.split(contentType != null ? contentType : "", ",")).map(Integer::parseInt).toList(),
                    Arrays.stream(StringUtils.split(isOriginal != null ? isOriginal : "", ",")).map(Integer::parseInt).toList(),
                    imageTextMode,
                    Arrays.stream(StringUtils.split(secondTrades != null ? secondTrades : "", ",")).toList(),
                    entity.getAuthorFollowersCountMin(),
                    entity.getAuthorFollowersCountMax(),
                    Arrays.stream(StringUtils.split(mediaLevel != null ? mediaLevel : "", ",")).toList());

            return responseDto;
        } catch (Exception e) {
            logger.error("Error converting Entity to ResponseDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert Entity to ResponseDto", e);
        }
    }

    public void updateEntityFromDto(MonitorConfigurationUpdateDto updateDto, MonitorConfiguration entity) {
        try {
            if (updateDto.planId() != null) {
                entity.setPlanId(updateDto.planId());
            }
            if (updateDto.time() != null) {
                entity.setTime(updateDto.time());
            }
            if (updateDto.startTime() != null) {
                entity.setStartTime(updateDto.startTime());
            }
            if (updateDto.endTime() != null) {
                entity.setEndTime(updateDto.endTime());
            }
            if (updateDto.sortRule() != null) {
                entity.setSortRule(updateDto.sortRule());
            }
            if (updateDto.sensitivityType() != null) {
                entity.setSensitivityType(updateDto.sensitivityType());
            }
            if (updateDto.similarityDisplayRule() != null) {
                entity.setSimilarityDisplayRule(updateDto.similarityDisplayRule());
            }
            if (updateDto.matchMethod() != null) {
                entity.setMatchMethod(updateDto.matchMethod());
            }
            List<String> mediaTypes = updateDto.mediaTypes();
            if (!CollectionUtils.isEmpty(mediaTypes)) {
                entity.setMediaType(StringUtils.join(mediaTypes, ","));
            }
            List<String> mediaTypeSeconds = updateDto.mediaTypeSeconds();
            if (!CollectionUtils.isEmpty(mediaTypeSeconds)) {
                entity.setMediaTypeSecond(StringUtils.join(mediaTypeSeconds, ","));
            }

            List<String> mediaTypeThirds = updateDto.mediaTypeThirds();
            if (!CollectionUtils.isEmpty(mediaTypeThirds)) {
                entity.setMediaTypeThird(StringUtils.join(mediaTypeThirds, ","));
            }

            List<Integer> contentTypes = updateDto.contentTypes();
            if (!CollectionUtils.isEmpty(contentTypes)) {
                entity.setContentType(StringUtils.join(contentTypes, ","));
            }
            List<Integer> originals = updateDto.isOriginals();
            if (!CollectionUtils.isEmpty(originals)) {
                entity.setIsOriginal(StringUtils.join(originals, ","));
            }
            if (updateDto.imageTextMode() != null) {
                entity.setImageTextMode(updateDto.imageTextMode());
            }
            List<String> secondTrades = updateDto.secondTrades();
            if (!CollectionUtils.isEmpty(secondTrades)) {
                entity.setSecondTrades(StringUtils.join(secondTrades, ","));
            }
            if (updateDto.authorFollowersCountMin() != null) {
                entity.setAuthorFollowersCountMin(updateDto.authorFollowersCountMin());
            }
            if (updateDto.authorFollowersCountMax() != null) {
                entity.setAuthorFollowersCountMax(updateDto.authorFollowersCountMax());
            }
            List<String> mediaLevels = updateDto.mediaLevels();
            if (!CollectionUtils.isEmpty(mediaLevels)) {
                entity.setMediaLevel(StringUtils.join(mediaLevels, ","));
            }
        } catch (Exception e) {
            logger.error("Error updating Entity from Dto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update Entity from Dto", e);
        }
    }
}
