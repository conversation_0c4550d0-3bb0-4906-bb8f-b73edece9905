package com.czb.hn.dto.monitor;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public record MonitorConfigurationResponseDto(
                @Schema(description = "Configuration ID") Long id,

                @Schema(description = "Associated plan ID", example = "1") Long planId,

                @Schema(description = "Monitor time", example = "1") Integer time,

                @Schema(description = "Start time", example = "2025-07-01 00:00:00") String startTime,

                @Schema(description = "End time", example = "2025-07-01 23:59:59") String endTime,

                @Schema(description = "Sort rule", example = "1") Integer sortRule,

                @Schema(description = "Information sensitivity type", example = "0") Integer sensitivityType,

                @Schema(description = "Similarity display rule", example = "false") Boolean similarityDisplayRule,

                @Schema(description = "Match method", example = "0") Integer matchMethod,

                @Schema(description = "Media type (deprecated, use sourceTypeHierarchy instead)") List<String> mediaTypes,

                @Schema(description = "Media type second (deprecated, use sourceTypeHierarchy instead)") List<String> mediaTypeSeconds,

                @Schema(description = "Media type third (deprecated, use sourceTypeHierarchy instead)") List<String> mediaTypeThirds,

                @Schema(description = "Content type") List<Integer> contentTypes,

                @Schema(description = "Is original", example = "0") List<Integer> isOriginals,

                @Schema(description = "Image text mode", example = "0") Integer imageTextMode,

                @Schema(description = "Second trades") List<String> secondTrades,

                @Schema(description = "Author followers count min", example = "0") Long authorFollowersCountMin,

                @Schema(description = "Author followers count max", example = "100000000") Long authorFollowersCountMax,

                @Schema(description = "Media level") List<String> mediaLevels) {
}
