package com.czb.hn.dto.monitor;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import javax.annotation.Nullable;
import java.util.List;

public record MonitorConfigurationUpdateDto(

                @Schema(description = "Associated plan ID", example = "1") Long planId,

                @Schema(description = "Monitor time", example = "2", allowableValues = {
                                "0", "1", "2", "3" }) Integer time,

                @Schema(description = "Start time", example = "2025-07-01 00:00:00") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @Nullable String startTime,

                @Schema(description = "End time", example = "2025-07-01 23:59:59") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @Nullable String endTime,

                @Schema(description = "Sort rule", example = "1", allowableValues = { "1", "2", "3",
                                "4" }) Integer sortRule,

                @Schema(description = "Information sensitivity type", example = "0", allowableValues = { "1", "2",
                                "3" }) @Nullable Integer sensitivityType,

                @Schema(description = "Similarity display rule", example = "false") Boolean similarityDisplayRule,

                @Schema(description = "Match method", example = "0", allowableValues = { "0", "1",
                                "2" }) Integer matchMethod,

                // 保留原字段以兼容现有API
                @Schema(description = "Media type (deprecated, use sourceTypeHierarchy instead)") @Nullable List<String> mediaTypes,

                @Schema(description = "Media type second (deprecated, use sourceTypeHierarchy instead)") @Nullable List<String> mediaTypeSeconds,

                @Schema(description = "Media type second (deprecated, use sourceTypeHierarchy instead)") @Nullable List<String> mediaTypeThirds,

                @Schema(description = "Content type", examples = { "1", "2" }, allowableValues = { "1", "2", "3",
                                "4" }) @Nullable List<Integer> contentTypes,

                @Schema(description = "Is original" , allowableValues = { "1",
                                "2" }) @Nullable List<Integer> isOriginals,

                @Schema(description = "Image text mode", example = "1", allowableValues = { "1",
                                "2" }) @Nullable Integer imageTextMode,

                @Schema(description = "Second trades") @Nullable List<String> secondTrades,

                @Schema(description = "Author followers count min", example = "0") @Nullable Long authorFollowersCountMin,

                @Schema(description = "Author followers count max", example = "100000000") @Nullable Long authorFollowersCountMax,

                @Schema(description = "Media level") @Nullable List<String> mediaLevels){
        public MonitorConfigurationUpdateDto {
                if (planId == null) {
                        throw new IllegalArgumentException("Plan ID cannot be null.");
                }
                if (time == null) {
                        throw new IllegalArgumentException("Time cannot be null.");
                }
                if (sortRule == null) {
                        throw new IllegalArgumentException("Sort rule cannot be null.");
                }
                if (matchMethod == null) {
                        throw new IllegalArgumentException("Match method cannot be null.");
                }
        }
}
