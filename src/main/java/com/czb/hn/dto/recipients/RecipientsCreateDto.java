package com.czb.hn.dto.recipients;

import io.swagger.v3.oas.annotations.media.Schema;

public record RecipientsCreateDto(
        @Schema(description = "方案Id")
        Long planId,

        @Schema(description = "系统类型")
        Integer systemType,

        @Schema(description = "接收类型   EMAIL SMS")
        String receiveType,

        @Schema(description = "名称")
        String name,

        @Schema(description = "地址")
        String address
) {
    public RecipientsCreateDto {
        if (planId == null) {
            throw new IllegalArgumentException("planId cannot be null");
        }
        if (systemType == null) {
            throw new IllegalArgumentException("systemType cannot be null");
        }
        if (receiveType == null) {
            throw new IllegalArgumentException("receiveType cannot be null");
        }
        if (name == null) {
            throw new IllegalArgumentException("name cannot be null");
        }
        if (address == null) {
            throw new IllegalArgumentException("address cannot be null");
        }
        if (receiveType.equalsIgnoreCase("email") && !address.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
            throw new IllegalArgumentException("Invalid email address format");
        }
        if (receiveType.equalsIgnoreCase("sms") && !address.matches("^1[3-9]\\d{9}$")) {
            throw new IllegalArgumentException("Invalid phone number format");
        }
    }
}
