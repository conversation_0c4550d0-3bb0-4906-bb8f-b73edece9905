package com.czb.hn.dto;


import com.czb.hn.jpa.securadar.entity.DictionaryItem;

public class DictionaryItemConverter {

    public static DictionaryItemDTO convertToDTO(DictionaryItem item) {
        DictionaryItemDTO itemDTO = new DictionaryItemDTO();
        itemDTO.setId(item.getId());
        itemDTO.setItemName(item.getItemName());
        itemDTO.setItemType(item.getItemType());
        itemDTO.setItemValue(item.getItemValue());
        itemDTO.setDescription(item.getDescription());
        return itemDTO;
    }

    public static DictionaryItem convertToEntity(DictionaryItemDTO itemDTO) {
        DictionaryItem item = new DictionaryItem();
        item.setId(itemDTO.getId());
        item.setItemName(itemDTO.getItemName());
        item.setItemType(itemDTO.getItemType());
        item.setItemValue(itemDTO.getItemValue());
        item.setDescription(itemDTO.getDescription());
        return item;
    }
}
