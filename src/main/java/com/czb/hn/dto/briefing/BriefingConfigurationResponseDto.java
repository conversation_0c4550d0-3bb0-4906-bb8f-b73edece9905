package com.czb.hn.dto.briefing;

import com.czb.hn.dto.briefing.config.ContentSettingsDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import io.swagger.v3.oas.annotations.media.Schema;

public record BriefingConfigurationResponseDto(
    @Schema(description = "Configuration ID")
    Long id,

    @Schema(description = "Configuration name", example = "华能资本")
    String name,

    @Schema(description = "Associated plan ID", example = "1")
    Long planId,

    @Schema(description = "Whether the configuration is active", example = "true")
    Boolean isActive,

    @Schema(description = "Content filtering settings")
    ContentSettingsDto contentSettings,

    @Schema(description = "Whether the daily briefing is active", example = "true")
    Boolean dailyBriefingIsActive,

    @Schema(description = "Daily briefing reception settings")
    BriefingReceptionSettingsDto dailyReceptionSettings,

    @Schema(description = "Whether the weekly briefing is active", example = "true")
    Boolean weeklyBriefingIsActive,

    @Schema(description = "Weekly briefing reception settings")
    BriefingReceptionSettingsDto weeklyReceptionSettings,

    @Schema(description = "Whether the monthly briefing is active", example = "true")
    Boolean monthlyBriefingIsActive,

    @Schema(description = "Monthly briefing reception settings")
    BriefingReceptionSettingsDto monthlyReceptionSettings

    ) {
}
