package com.czb.hn.dto.alert.config;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * DTO for alert level classification settings
 * Defines how alerts are classified into general/moderate/severe levels
 */
public record LevelSettingsDto(

        @Schema(description = "Source level classifications condition is enabled", example = "true") boolean sourceLevelEnabled,

        @Schema(description = "Source level classifications mapping source types to alert levels", example = "{\"央级\": \"SEVERE\", \"省级\": \"MODERATE\", \"地市\": \"GENERAL\"}") Map<String, String> sourceLevel,

        @Schema(description = "Interaction count thresholds for different levels", example = "{\"general\": {\"min\": 0, \"max\": 99}, \"moderate\": {\"min\": 100, \"max\": 999}, \"severe\": {\"min\": 1000, \"max\": null}}") LevelThresholdsDto interactionThresholds,

        @Schema(description = "Follower count thresholds for different levels", example = "{\"general\": {\"min\": 0, \"max\": 999}, \"moderate\": {\"min\": 1000, \"max\": 9999}, \"severe\": {\"min\": 10000, \"max\": null}}") LevelThresholdsDto fansThresholds,

        @Schema(description = "Read count thresholds for different levels", example = "{\"general\": {\"min\": 0, \"max\": 4999}, \"moderate\": {\"min\": 5000, \"max\": 49999}, \"severe\": {\"min\": 50000, \"max\": null}}") LevelThresholdsDto readThresholds,

        @Schema(description = "Similar article count thresholds for different levels", example = "{\"general\": {\"min\": 0, \"max\": 9}, \"moderate\": {\"min\": 10, \"max\": 49}, \"severe\": {\"min\": 50, \"max\": null}}") LevelThresholdsDto similarArticleThresholds) {
    // Compact canonical constructor for validation
    public LevelSettingsDto {
        // Validate source level mappings
        if (sourceLevel != null) {
            List<String> validSourceTypes = List.of("央级", "省级", "地市", "重点", "中小",
                    "企业商业");
            List<String> validLevels = List.of("GENERAL", "MODERATE", "SEVERE");

            for (Map.Entry<String, String> entry : sourceLevel.entrySet()) {
                if (!validSourceTypes.contains(entry.getKey())) {
                    throw new IllegalArgumentException(
                            "Invalid source type: " + entry.getKey() + ". Must be one of: " + validSourceTypes);
                }
                if (!validLevels.contains(entry.getValue())) {
                    throw new IllegalArgumentException(
                            "Invalid level: " + entry.getValue() + ". Must be one of: " + validLevels);
                }
            }
        }

        // Validate threshold configurations
        if (interactionThresholds != null) {
            validateThresholds(interactionThresholds, "interactionThresholds");
        }
        if (fansThresholds != null) {
            validateThresholds(fansThresholds, "fansThresholds");
        }
        if (readThresholds != null) {
            validateThresholds(readThresholds, "readThresholds");
        }
        if (similarArticleThresholds != null) {
            validateThresholds(similarArticleThresholds, "similarArticleThresholds");
        }
    }

    private static void validateThresholds(LevelThresholdsDto thresholds, String fieldName) {
        if (thresholds.general() != null && thresholds.moderate() != null) {
            if (thresholds.general().max() != null && thresholds.moderate().min() != null) {
                if (thresholds.general().max() >= thresholds.moderate().min()) {
                    throw new IllegalArgumentException(fieldName + ": general max must be less than moderate min");
                }
            }
        }
        if (thresholds.moderate() != null && thresholds.severe() != null) {
            if (thresholds.moderate().max() != null && thresholds.severe().min() != null) {
                if (thresholds.moderate().max() >= thresholds.severe().min()) {
                    throw new IllegalArgumentException(fieldName + ": moderate max must be less than severe min");
                }
            }
        }
    }

    /**
     * DTO for level threshold ranges
     */
    public record LevelThresholdsDto(
            @Schema(description = "level condition is enabled", example = "true") boolean enabled,

            @Schema(description = "General level threshold range", example = "{\"min\": 0, \"max\": 99}") ThresholdRangeDto general,

            @Schema(description = "Moderate level threshold range", example = "{\"min\": 100, \"max\": 999}") ThresholdRangeDto moderate,

            @Schema(description = "Severe level threshold range", example = "{\"min\": 1000, \"max\": null}") ThresholdRangeDto severe) {
        // No additional validation needed here
    }

    /**
     * DTO for threshold range definition
     */
    public record ThresholdRangeDto(
            @Schema(description = "Minimum threshold value (inclusive)", example = "0") Long min,

            @Schema(description = "Maximum threshold value (exclusive, null means no upper limit)", example = "999") Long max) {
        public ThresholdRangeDto {
            if (min != null && min < 0) {
                throw new IllegalArgumentException("Minimum threshold cannot be negative");
            }
            if (max != null && max < 0) {
                throw new IllegalArgumentException("Maximum threshold cannot be negative");
            }
            if (min != null && max != null && min >= max) {
                throw new IllegalArgumentException("Minimum threshold must be less than maximum threshold");
            }
        }
    }
}
