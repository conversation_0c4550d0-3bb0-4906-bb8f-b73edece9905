package com.czb.hn.dto.response.briefing;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * 聚合后的趋势图DTO
 * 每个时间点包含三类数据量
 */
public record HistogramGroupDto(
    @Schema(description = "时间", example = "2024-07-04 00:00:00")
    LocalDateTime time,
    @Schema(description = "敏感数据量")
    long sensitiveCount,
    @Schema(description = "中性数据量")
    long neutralCount,
    @Schema(description = "非敏感数据量")
    long nonSensitiveCount
) {
    public HistogramGroupDto {
        if (time == null) throw new IllegalArgumentException("时间不能为空");
        if (sensitiveCount < 0 || neutralCount < 0 || nonSensitiveCount < 0)
            throw new IllegalArgumentException("数据量不能为负数");
    }
} 