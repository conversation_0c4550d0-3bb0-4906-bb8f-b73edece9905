package com.czb.hn.service.job;

/**
 * 任务信息接口
 * 用于任务处理器接收任务信息
 */
public interface JobInfo {
    
    /**
     * 获取任务ID
     *
     * @return 任务ID
     */
    Long getId();
    
    /**
     * 获取任务名称
     *
     * @return 任务名称
     */
    String getName();
    
    /**
     * 获取任务参数（JSON格式）
     *
     * @return 任务参数
     */
    String getJobParams();
    
    /**
     * 设置任务参数（JSON格式）
     *
     * @param jobParams 任务参数
     */
    void setJobParams(String jobParams);
    
    /**
     * 获取任务周期类型
     *
     * @return 周期类型
     */
    String getCycleType();
    
    /**
     * 获取执行日期
     *
     * @return 执行日期
     */
    String getExecutionDay();
    
    /**
     * 获取方案ID
     *
     * @return 方案ID
     */
    Long getPlanId();
} 