package com.czb.hn.service.cleaner;

import com.czb.hn.jpa.securadar.entity.SinaNewsDwdEntity;
import com.czb.hn.jpa.securadar.entity.SinaNewsOdsEntity;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.EmotionType;

import java.time.LocalDate;
import java.util.List;

/**
 * 新浪舆情数据清洗服务接口
 * 负责将ODS层数据清洗转换为DWD层数据
 */
public interface SinaNewsCleanerService {

    /**
     * 处理单条ODS记录
     * 
     * @param source ODS层数据
     * @return 处理后的DWD层数据
     */
    SinaNewsDwdEntity processRecord(SinaNewsOdsEntity source);

    /**
     * 处理所有未处理的记录
     * 
     * @return 处理的记录数
     */
    int processAllUnprocessedRecords();

    /**
     * 定时调度处理任务
     */
    void scheduledProcessing();

    /**
     * 根据日期查找DWD记录
     * 
     * @param date 日期
     * @return DWD记录列表
     */
    List<SinaNewsDwdEntity> findByDate(LocalDate date);

    /**
     * 根据内容ID查找已处理的记录
     *
     * @param contentId 内容ID
     * @return 处理后的DWD记录
     */
    SinaNewsDwdEntity findByContentId(String contentId);

}