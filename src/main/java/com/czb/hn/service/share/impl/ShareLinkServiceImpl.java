package com.czb.hn.service.share.impl;

import com.czb.hn.dto.ShareLinkDto;
import com.czb.hn.jpa.securadar.entity.ShareLinkEntity;
import com.czb.hn.jpa.securadar.repository.ShareLinkRepository;
import com.czb.hn.service.share.ShareLinkService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.Optional;

@Service
public class ShareLinkServiceImpl implements ShareLinkService {

    private final ShareLinkRepository shareLinkRepository;

    public ShareLinkServiceImpl(ShareLinkRepository shareLinkRepository) {
        this.shareLinkRepository = shareLinkRepository;
    }

    @Override
    public ShareLinkDto createShareLink(String originUrl,  long expireMillis, String createUserId) {
        ShareLinkEntity link = new ShareLinkEntity();
        link.setShareCode(UUID.randomUUID().toString().replace("-", ""));
        link.setOriginUrl(originUrl);
        link.setExpireTime(LocalDateTime.now().plusNanos(expireMillis * 1_000_000));
        link.setStatus(0);
        link.setCreateUserId(createUserId);
        link.setCreatedAt(LocalDateTime.now());
        link.setUpdatedAt(LocalDateTime.now());
        shareLinkRepository.save(link);
        return new ShareLinkDto(link.getShareCode(), link.getOriginUrl(), link.getExpireTime());
    }

    @Override
    public ShareLinkDto validateShareCode(String shareCode) {
        Optional<ShareLinkEntity> opt = shareLinkRepository.findByShareCode(shareCode);
        ShareLinkEntity link = opt.orElseThrow(() -> new IllegalArgumentException("分享码不存在"));
        if (link.getStatus() != 0) throw new IllegalArgumentException("分享码已撤销");
        if (link.getExpireTime().isBefore(LocalDateTime.now())) throw new IllegalArgumentException("分享码已过期");
        return new ShareLinkDto(link.getShareCode(), link.getOriginUrl(), link.getExpireTime());
    }
}