package com.czb.hn.service.business;

import com.czb.hn.dto.EnterpriseDTO;
import com.czb.hn.dto.PlanCreateDTO;
import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.PlanUpdateDTO;

import java.util.List;

public interface PlanService {
    /**
     * Create a new monitoring plan
     *
     * @param planCreateDTO the plan creation data
     * @return the created plan DTO
     */
    PlanDTO createPlan(PlanCreateDTO planCreateDTO);

    /**
     * Get a plan by its ID
     *
     * @param id the plan ID
     * @return the plan DTO
     */
    PlanDTO getPlanById(Long id);

    /**
     * Update an existing plan
     *
     * @param id            the plan ID
     * @param planUpdateDTO the plan update data
     * @return the updated plan DTO
     */
    PlanDTO updatePlan(Long id, PlanUpdateDTO planUpdateDTO);

    /**
     * Delete a plan by its ID
     *
     * @param id the plan ID
     */
    void deletePlan(Long id);

    /**
     * Get all plans
     *
     * @return list of all plans
     */
    List<PlanDTO> getAllPlans();

    /**
     * Get plans visible to a specific enterprise
     *
     * @param enterpriseId the enterprise ID
     * @return list of plans visible to the enterprise
     */
    List<PlanDTO> getPlansByEnterpriseId(String enterpriseId);

    /**
     * Get complete keyword parse result for a specific plan
     *
     * @param planId the plan ID
     * @return complete keyword parse result with logic
     */
    com.czb.hn.util.KeywordUtils.KeywordParseResult getPlanKeywordsWithLogic(Long planId);

    /**
     * Get all visible enterprises
     * 
     * @return
     */
    List<EnterpriseDTO> getVisibleEnterprise();

}
