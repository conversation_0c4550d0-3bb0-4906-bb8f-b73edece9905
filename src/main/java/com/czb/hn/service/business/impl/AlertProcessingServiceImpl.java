package com.czb.hn.service.business.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertProcessingService;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.AlertRuleEngine;
import com.czb.hn.service.business.KeywordTrackingService;
import com.czb.hn.util.AlertResultMapper;
import com.czb.hn.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Alert Processing Service Implementation
 * Processes Elasticsearch documents against alert configurations
 * Uses existing SinaNewsElasticsearchSearchService patterns
 */
@Service
@Slf4j
public class AlertProcessingServiceImpl implements AlertProcessingService {

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Autowired
    private AlertRuleEngine ruleEngine;

    @Autowired
    private KeywordTrackingService keywordTrackingService;

    @Autowired
    private AlertResultMapper alertResultMapper;

    @Autowired
    private AlertPushService alertPushService;

    @Value("${alert.processing.batch-size:100}")
    private int batchSize;

    @Value("${alert.processing.elasticsearch.index:sina_news}")
    private String elasticsearchIndex;

    @Value("${alert.processing.time-window-hours:24}")
    private int timeWindowHours;

    @Override
    @Scheduled(cron = "${alert.processing.cron:0 */3 * * * *}")
    public void processAlerts() {
        try {
            log.info("Starting scheduled alert processing");
            long startTime = System.currentTimeMillis();

            // Get all active enterprises
            List<String> activeEnterprises = getActiveEnterprises();

            // Process each enterprise separately for tenant isolation
            int totalAlerts = activeEnterprises.parallelStream()
                    .mapToInt(enterpriseId -> processEnterpriseAlerts(enterpriseId).size())
                    .sum();

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("Completed scheduled alert processing for {} enterprises, generated {} alerts in {}ms",
                    activeEnterprises.size(), totalAlerts, processingTime);

        } catch (Exception e) {
            log.error("Error in scheduled alert processing: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<AlertResultResponseDto> processEnterpriseAlerts(String enterpriseId) {
        log.info("Processing alerts for enterprise: {}", enterpriseId);

        List<AlertResultResponseDto> results = new ArrayList<>();

        try {
            // Get active configurations for this enterprise
            List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                    .getActiveConfigurationsByEnterprise(enterpriseId);

            if (configs.isEmpty()) {
                log.debug("No active configurations found for enterprise: {}", enterpriseId);
                return results;
            }

            // Get recent documents from Elasticsearch for processing
            List<SinaNewsDocument> recentDocuments = getRecentDocumentsFromElasticsearch();

            if (recentDocuments.isEmpty()) {
                log.debug("No recent documents found in Elasticsearch for processing");
                return results;
            }

            log.info("Processing {} documents against {} configurations for enterprise: {}",
                    recentDocuments.size(), configs.size(), enterpriseId);

            // Process in batches
            for (int i = 0; i < recentDocuments.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, recentDocuments.size());
                List<SinaNewsDocument> batch = recentDocuments.subList(i, endIndex);

                List<AlertResultResponseDto> batchResults = processBatch(configs, batch);
                results.addAll(batchResults);
            }

            log.info("Generated {} alerts for enterprise: {}", results.size(), enterpriseId);

        } catch (Exception e) {
            log.error("Error processing alerts for enterprise {}: {}", enterpriseId, e.getMessage(), e);
        }

        return results;
    }

    @Override
    public List<AlertResultResponseDto> processPlanAlerts(Long planId) {
        log.info("Processing alerts for plan: {}", planId);

        try {
            // Get active configurations for this plan
            List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                    .getActiveConfigurationsByPlan(planId);

            if (configs.isEmpty()) {
                log.debug("No active configurations found for plan: {}", planId);
                return new ArrayList<>();
            }

            // Get recent documents from Elasticsearch
            List<SinaNewsDocument> recentDocuments = getRecentDocumentsFromElasticsearch();

            // Process documents against plan configurations
            return processBatch(configs, recentDocuments);

        } catch (Exception e) {
            log.error("Error processing alerts for plan {}: {}", planId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AlertResultResponseDto> processConfigurationAlerts(Long configurationId) {
        log.info("Processing alerts for configuration: {}", configurationId);

        try {
            // Get specific configuration
            var configOpt = alertConfigConsumerService.getActiveConfigurationById(configurationId);
            if (configOpt.isEmpty()) {
                log.debug("Configuration not found or inactive: {}", configurationId);
                return new ArrayList<>();
            }

            List<AlertConfigurationResponseDto> configs = List.of(configOpt.get());

            // Get recent documents from Elasticsearch
            List<SinaNewsDocument> recentDocuments = getRecentDocumentsFromElasticsearch();

            // Process documents against this configuration
            return processBatch(configs, recentDocuments);

        } catch (Exception e) {
            log.error("Error processing alerts for configuration {}: {}", configurationId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getActiveEnterprises() {
        try {
            // Get all active configurations and extract unique enterprise IDs
            return alertConfigConsumerService.getAllActiveConfigurations()
                    .stream()
                    .map(AlertConfigurationResponseDto::enterpriseId)
                    .distinct()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting active enterprises: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public ProcessingStatistics getProcessingStatistics(String enterpriseId) {
        // Implementation for getting processing statistics
        // This would typically query processing logs and metrics
        return new ProcessingStatistics(
                enterpriseId, 0, 0, 0, 0L, "NOT_IMPLEMENTED");
    }

    /**
     * Get recent documents from Elasticsearch for processing
     * Uses time window to limit processing scope
     */
    private List<SinaNewsDocument> getRecentDocumentsFromElasticsearch() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(timeWindowHours);

            // Build query for recent documents
            BoolQuery.Builder boolQuery = new BoolQuery.Builder()
                    .filter(f -> f.range(r -> r
                            .field("publishTime")
                            .gte(JsonData.of(DateTimeUtil.formatToStandardString(cutoffTime)))));

            Query query = boolQuery.build()._toQuery();
            log.info("Elasticsearch Query: {}", query);

            SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(elasticsearchIndex)
                    .query(query)
                    .size(10000) // Adjust based on your needs
                    .sort(sort -> sort.field(f -> f.field("publishTime")
                            .order(co.elastic.clients.elasticsearch._types.SortOrder.Desc))));

            SearchResponse<SinaNewsDocument> response = elasticsearchClient.search(searchRequest,
                    SinaNewsDocument.class);

            return response.hits().hits().stream()
                    .map(Hit::source)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error fetching recent documents from Elasticsearch: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Process a batch of documents against configurations
     */
    private List<AlertResultResponseDto> processBatch(
            List<AlertConfigurationResponseDto> configs,
            List<SinaNewsDocument> documents) {

        List<AlertResultResponseDto> results = new ArrayList<>();

        for (SinaNewsDocument document : documents) {
            for (AlertConfigurationResponseDto config : configs) {
                try {
                    // Check if alert already exists to prevent duplicates
                    if (alertResultRepository.existsByEnterpriseIdAndOriginalContentIdAndConfigurationId(
                            config.enterpriseId(), document.getContentId(), config.id())) {
                        continue;
                    }

                    // Convert DTO to entity for rule evaluation
                    AlertConfiguration configEntity = alertResultMapper.toEntity(config);

                    // Evaluate rule against Elasticsearch document
                    if (ruleEngine.evaluateRule(configEntity, document)) {

                        // Create alert result
                        AlertResult alertResult = createAlertResult(config, document);
                        AlertResult saved = alertResultRepository.save(alertResult);

                        // Note: Push notifications are now handled by AlertNotificationScheduler
                        // in a decoupled manner based on reception settings

                        results.add(alertResultMapper.toResponseDto(saved));

                        log.debug("Created alert {} for document {} using configuration {}",
                                saved.getId(), document.getContentId(), config.id());
                    }

                } catch (Exception e) {
                    log.error("Error processing document {} with configuration {}: {}",
                            document.getContentId(), config.id(), e.getMessage(), e);
                }
            }
        }

        return results;
    }

    /**
     * Create AlertResult from configuration and Elasticsearch document
     */
    private AlertResult createAlertResult(AlertConfigurationResponseDto config, SinaNewsDocument document) {
        // Track involved keywords using existing patterns
        // 重新计算关键字词语数
        Map<String, Integer> keywordsCount = keywordTrackingService.extractInvolvedKeywords(config, document);

        // Determine warning level
        AlertResult.WarningLevel warningLevel = ruleEngine.determineWarningLevel(config.levelSettings(), document);

        // todo 需要从配置中读取 推断内容匹配类型

        return AlertResult.builder()
                .enterpriseId(config.enterpriseId())
                .planId(config.planId())
                .configurationId(config.id())
                .title(StringUtils.isBlank(document.getTitle()) ? "" : document.getTitle())
                .content(StringUtils.isBlank(document.getContent()) ? "" : document.getContent())
                .involvedKeywords(serializeInvolvedKeywords(keywordsCount))
                .informationSensitivityType(InformationSensitivityType.fromInteger(document.getSensitivityType()))
                .sourceType(SourceType.fromString(document.getMediaType()))
                .contentType(ContentType.fromInteger(document.getContentTypes()))
                .contentMatchType(ContentMatchType.MAIN_TXT) // todo 需要从配置中读取
                .contentCategory(ContentCategory.fromInteger(document.getIsOriginal()))
                .mediaLevel(MediaLevel.fromChineseValue(document.getMediaLevel()))
                .warningLevel(warningLevel)
                .source(StringUtils.isBlank(document.getCaptureWebsite()) ? "" : document.getCaptureWebsite())
                .provincial(StringUtils.isBlank(document.getPublishProvince()) ? "" : document.getPublishProvince())
                .warningTime(LocalDateTime.now())
                .similarArticleCount(document.getSimilarityNum() != null ? document.getSimilarityNum().intValue() : 0)
                .originalContentId(document.getContentId())
                .processingVersion("1.0")
                .ruleSnapshot(createRuleSnapshot(config))
                .createdBy("SYSTEM")
                .build();
    }

    /**
     * Serialize involved keywords to JSON
     */
    private String serializeInvolvedKeywords(Map<String, Integer> keywords) {
        try {
            return alertResultMapper.getObjectMapper().writeValueAsString(keywords);
        } catch (Exception e) {
            log.error("Error serializing involved keywords: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * Create rule snapshot for audit purposes
     */
    private String createRuleSnapshot(AlertConfigurationResponseDto config) {
        try {
            return alertResultMapper.getObjectMapper().writeValueAsString(config);
        } catch (Exception e) {
            log.error("Error creating rule snapshot: {}", e.getMessage());
            return "{}";
        }
    }
}
