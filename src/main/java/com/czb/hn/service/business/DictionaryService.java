package com.czb.hn.service.business;


import com.czb.hn.dto.DictionaryItemDTO;

import java.util.List;
import java.util.Map;

public interface DictionaryService {

    /**
     * 获取所有字典类型
     * @return
     */
    List<String> getAllItemTypes();

    /**
     * 获取所有字典项
     * @return
     */
    Map<String, List<DictionaryItemDTO>> getAllItems();

    /**
     * 根据类型获取字典项
     * @param itemType
     * @return
     */
    List<DictionaryItemDTO> getItemsByType(String itemType);

    /**
     * 根据类型列表获取字典项
     * @param itemTypes
     * @return
     */
    Map<String, List<DictionaryItemDTO>> getItemsByTypes(List<String> itemTypes);

    /**
     * 根据id获取字典项
     * @param id
     * @return
     */
    DictionaryItemDTO getItemById(Long id);

    /**
     * 根据类型和值获取字典项
     * @param itemType
     * @param itemValue
     * @return
     */
    DictionaryItemDTO getItemByItemTypeAndItemValue(String itemType, String itemValue);

    /**
     * 添加字典项
     * @param item
     * @return
     */
    DictionaryItemDTO addItem(DictionaryItemDTO item);

    /**
     * 更新字典项
     * @param id
     * @param item
     * @return
     */
    DictionaryItemDTO updateItem(Long id, DictionaryItemDTO item);

    /**
     * 删除字典项
     * @param id
     */
    void deleteItem(Long id);

}
