package com.czb.hn.service.business;

import com.czb.hn.dto.briefing.BriefingConfigurationCreateDto;
import com.czb.hn.dto.briefing.BriefingConfigurationResponseDto;
import com.czb.hn.dto.briefing.BriefingConfigurationUpdateDto;

public interface BriefingConfigurationService {

    /**
     * Get briefing configurations by plan ID
     *
     * @param planId Plan ID
     * @return Briefing configuration response DTO
     */
    BriefingConfigurationResponseDto getConfigurationsByPlanId(Long planId);

    /**
     * Check if the plan ID is available for a briefing configuration
     *
     * @param planId Plan ID
     * @return True if the plan ID is available, false otherwise
     */
    Boolean isConfigurationPlanIdAvailable(Long planId);

    /**
     * Update an existing briefing configuration
     *
     * @param id        Configuration ID
     * @param updateDto Configuration update data
     * @return Updated configuration response
     */
    BriefingConfigurationResponseDto updateConfiguration(Long id, BriefingConfigurationUpdateDto updateDto);
}
