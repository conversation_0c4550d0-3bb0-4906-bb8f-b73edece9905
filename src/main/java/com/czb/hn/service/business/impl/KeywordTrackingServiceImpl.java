package com.czb.hn.service.business.impl;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.AlertKeywordsDto;
import com.czb.hn.dto.alert.config.ContentSettingsDto;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.service.business.KeywordTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.ahocorasick.trie.Emit;
import org.ahocorasick.trie.Trie;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Keyword Tracking Service Implementation
 * Extends SinaNewsMonitor patterns for alert processing
 * Works with Elasticsearch documents as the primary data source
 */
@Service
@Slf4j
public class KeywordTrackingServiceImpl implements KeywordTrackingService {

    @Override
    public Map<String, Integer> extractInvolvedKeywords(AlertConfigurationResponseDto config, SinaNewsDocument document) {
        Map<String, Integer> countMap = new HashMap<>();
        if (config == null) {
            return countMap;
        }
        AlertKeywordsDto alertKeywordsDto = config.alertKeywords();
        ContentSettingsDto contentSettingsDto = config.contentSettings();


        List<String> keywords = alertKeywordsDto.keywords();

        if (CollectionUtils.isEmpty(keywords)) {
            return countMap;
        }

        // 构造文本
        String content = buildSearchableContent(document, contentSettingsDto);

        Trie trie = Trie.builder()
                .addKeywords(keywords)
                .build();
        Collection<Emit> emits = trie.parseText(content);

        for (Emit emit : emits) {
            String keyword = emit.getKeyword();
            countMap.put(keyword, countMap.getOrDefault(keyword, 0) + 1);
        }

        return countMap;
    }

    @Override
    public List<Integer> findKeywordPositions(String content, String keyword) {
        List<Integer> positions = new ArrayList<>();

        if (content == null || keyword == null || content.isEmpty() || keyword.isEmpty()) {
            return positions;
        }

        String lowerContent = content.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        int index = 0;
        while ((index = lowerContent.indexOf(lowerKeyword, index)) != -1) {
            positions.add(index);
            index += lowerKeyword.length();
        }

        return positions;
    }

    @Override
    public Set<String> buildTargetKeywords(AlertConfigurationResponseDto config) {
        Set<String> targetKeywords = new HashSet<>();

        try {
            // Simple approach: just add all keywords from the alert configuration
            for (String keyword : config.alertKeywords().keywords()) {
                if (keyword != null && !keyword.trim().isEmpty()) {
                    targetKeywords.add(keyword.trim());
                }
            }

        } catch (Exception e) {
            log.error("Error building target keywords for configuration {}: {}",
                    config.id(), e.getMessage(), e);
        }

        return targetKeywords;
    }

    @Override
    public Map<String, Integer> extractHighlightKeywords(String content, Set<String> targetKeywords) {
        Map<String, Integer> keywordCounts = new HashMap<>();

        if (content == null || targetKeywords == null || content.isEmpty() || targetKeywords.isEmpty()) {
            return keywordCounts;
        }

        String lowerContent = content.toLowerCase();

        for (String keyword : targetKeywords) {
            String lowerKeyword = keyword.toLowerCase();
            int count = 0;
            int index = 0;

            while ((index = lowerContent.indexOf(lowerKeyword, index)) != -1) {
                count++;
                index += lowerKeyword.length();
            }

            if (count > 0) {
                keywordCounts.put(keyword, count);
            }
        }

        return keywordCounts;
    }

    /**
     * Build searchable content from Elasticsearch document
     * Similar to existing SinaNewsMonitor patterns
     */
    private String buildSearchableContent(SinaNewsDocument document, ContentSettingsDto contentSettingsDto) {
        StringBuilder content = new StringBuilder();

        if (contentSettingsDto != null) {
            Integer matchMethod = contentSettingsDto.matchMethod();
            ContentMatchType contentMatchType = ContentMatchType.fromInteger(matchMethod);
            if (ContentMatchType.TITLE.equals(contentMatchType)) {
                if (StringUtils.isNotBlank(document.getTitle())) {
                    content.append(document.getTitle());
                }
            } else if (ContentMatchType.MAIN_TXT.equals(contentMatchType)) {
                if (StringUtils.isNotBlank(document.getContent())) {
                    content.append(document.getContent());
                }
            } else {
                if (StringUtils.isNotBlank(document.getTitle())) {
                    content.append(document.getTitle()).append(" ");
                }
                if (StringUtils.isNotBlank(document.getContent())) {
                    content.append(document.getContent());
                }
            }
        } else {
            if (StringUtils.isNotBlank(document.getTitle())) {
                content.append(document.getTitle()).append(" ");
            }
            if (StringUtils.isNotBlank(document.getContent())) {
                content.append(document.getContent());
            }
        }

        return content.toString();
    }

    /**
     * Extract keywords with highlighting patterns
     * Adapts existing SinaNewsMonitor highlighting logic
     */
    public List<Map<String, Integer>> extractHighlightKeywordsWithPositions(
            String content, Set<String> targetKeywords) {

        List<Map<String, Integer>> result = new ArrayList<>();

        // Pattern to match highlighted content (if any)
        Pattern pattern = Pattern.compile("<strong>(.*?)</strong>");

        if (content != null) {
            Matcher matcher = pattern.matcher(content);
            Map<String, Integer> keywordCounts = new HashMap<>();

            while (matcher.find()) {
                String highlightedText = matcher.group(1);
                if (targetKeywords.contains(highlightedText)) {
                    keywordCounts.merge(highlightedText, 1, Integer::sum);
                }
            }

            // Convert to list of maps format (matching existing patterns)
            for (Map.Entry<String, Integer> entry : keywordCounts.entrySet()) {
                Map<String, Integer> keywordMap = new HashMap<>();
                keywordMap.put(entry.getKey(), entry.getValue());
                result.add(keywordMap);
            }
        }

        return result;
    }
}
