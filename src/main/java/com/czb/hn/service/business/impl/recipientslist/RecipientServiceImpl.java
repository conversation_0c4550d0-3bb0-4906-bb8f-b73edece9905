package com.czb.hn.service.business.impl.recipientslist;


import com.czb.hn.dto.recipients.RecipientsCreateDto;
import com.czb.hn.dto.recipients.RecipientsRequireDto;
import com.czb.hn.dto.recipients.RecipientsResponseDto;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.entity.Recipients;
import com.czb.hn.jpa.securadar.repository.RecipientsListRepository;
import com.czb.hn.service.business.RecipientService;
import com.czb.hn.util.RecipientsListMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class RecipientServiceImpl implements RecipientService {
    private static final Logger logger = LoggerFactory.getLogger(RecipientServiceImpl.class);

    @Autowired
    private RecipientsListRepository recipientsListRepository;

    @Autowired
    private RecipientsListMapper recipientsListMapper;

    @Override
    public List<RecipientsResponseDto> getRecipientsList(RecipientsRequireDto requireDto) {
        try {
            logger.info("Retrieving recipients list by plan id: {}, system type: {}, receive type: {}",
                    requireDto.planId(), requireDto.systemType(), requireDto.receiveType());
            List<Recipients> recipients = recipientsListRepository.findByPlanIdAndSystemTypeAndReceiveType(
                    requireDto.planId(), requireDto.systemType(), PushType.fromString(requireDto.receiveType()));
            logger.info("Successfully retrieved {} recipients list", recipients.size());
            return recipientsListMapper.toResponseDto(recipients);
        } catch (Exception e) {
            logger.error("Error retrieving recipients list by plan id {}: {}", requireDto.planId(), e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve recipients list: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean createRecipients(RecipientsCreateDto createDto) {
        try {
            logger.info("Creating recipients list: {}", createDto);
            Recipients createRecipientsList = recipientsListMapper.toCreateEntity(createDto);
            Recipients savedRecipientsList = recipientsListRepository.save(createRecipientsList);
            logger.info("Successfully created recipients list: {}", savedRecipientsList);
            return true;
        } catch (Exception e) {
            logger.error("Error creating recipients list: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create recipients list: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean deleteRecipients(Long id) {
        try {
            recipientsListRepository.deleteById(id);
            logger.info("Successfully deleted recipients list");
            return true;
        } catch (Exception e) {
            logger.error("Error deleting recipients list: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete recipients list", e);
        }
    }

    @Override
    public Boolean enable(Long id, boolean enable) {
        Optional<Recipients> recipientsOp = recipientsListRepository.findById(id);
        if (recipientsOp.isEmpty()) {
            return false;
        }
        Recipients recipients = recipientsOp.get();
        recipients.setEnable(enable);
        recipientsListRepository.save(recipients);
        return true;
    }
}
