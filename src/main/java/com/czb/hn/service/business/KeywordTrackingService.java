package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;

import java.util.List;
import java.util.Map;

/**
 * Keyword Tracking Service Interface
 * Extends SinaNewsMonitor patterns to track which keywords triggered alerts
 * Works with Elasticsearch documents as the primary data source
 */
public interface KeywordTrackingService {
    
    /**
     * Extract involved keywords from Elasticsearch document based on alert configuration
     * 
     * @param config Alert configuration containing keyword rules
     * @param document Elasticsearch document to analyze
     * @return List of keywords that triggered the alert
     */
    Map<String, Integer> extractInvolvedKeywords(AlertConfigurationResponseDto config, SinaNewsDocument document);
    
    /**
     * Find keyword positions in content for highlighting
     * 
     * @param content Content text to search
     * @param keyword Keyword to find
     * @return List of positions where keyword appears
     */
    List<Integer> findKeywordPositions(String content, String keyword);
    
    /**
     * Build target keywords set from configuration
     * Similar to existing SinaNewsMonitor implementation
     * 
     * @param config Alert configuration
     * @return Set of target keywords for matching
     */
    java.util.Set<String> buildTargetKeywords(AlertConfigurationResponseDto config);
    
    /**
     * Extract highlighted keywords from content
     * Adapts existing SinaNewsMonitor highlighting logic
     * 
     * @param content Content to analyze
     * @param targetKeywords Keywords to look for
     * @return Map of keywords with their occurrence counts
     */
    java.util.Map<String, Integer> extractHighlightKeywords(String content, java.util.Set<String> targetKeywords);
}
