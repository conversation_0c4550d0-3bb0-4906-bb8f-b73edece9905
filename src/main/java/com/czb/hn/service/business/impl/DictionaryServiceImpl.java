package com.czb.hn.service.business.impl;

import com.czb.hn.dto.DictionaryItemConverter;
import com.czb.hn.dto.DictionaryItemDTO;
import com.czb.hn.jpa.securadar.entity.DictionaryItem;
import com.czb.hn.jpa.securadar.repository.DictionaryItemRepository;
import com.czb.hn.service.business.DictionaryService;
import org.springframework.stereotype.Service;

import java.util.List;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DictionaryServiceImpl implements DictionaryService {

    private final DictionaryItemRepository dictionaryItemRepository;

    public DictionaryServiceImpl(DictionaryItemRepository dictionaryItemRepository) {
        this.dictionaryItemRepository = dictionaryItemRepository;
    }

    @Override
    public List<String> getAllItemTypes() {
        return dictionaryItemRepository.findDistinctItemType();
    }

    @Override
    public Map<String, List<DictionaryItemDTO>> getAllItems() {
        List<DictionaryItemDTO> dictionaryItemDTOList =  dictionaryItemRepository.findAll().stream().map(DictionaryItemConverter::convertToDTO).toList();
        return dictionaryItemDTOList.stream().collect(Collectors.groupingBy(DictionaryItemDTO::getItemType));
    }

    @Override
    public List<DictionaryItemDTO> getItemsByType(String itemType) {
        return dictionaryItemRepository.findByItemType(itemType).stream().map(DictionaryItemConverter::convertToDTO).toList();
    }

    @Override
    public Map<String, List<DictionaryItemDTO>> getItemsByTypes(List<String> itemTypes) {
        List<DictionaryItemDTO> dictionaryItemDTOList = dictionaryItemRepository.findByItemTypeIn(itemTypes).stream().map(DictionaryItemConverter::convertToDTO).toList();
        return dictionaryItemDTOList.stream().collect(Collectors.groupingBy(DictionaryItemDTO::getItemType));
    }

    @Override
    public DictionaryItemDTO getItemById(Long id) {
        Optional<DictionaryItem> optionalItem = dictionaryItemRepository.findById(id);
        return optionalItem.map(DictionaryItemConverter::convertToDTO).orElse(null);
    }

    @Override
    public DictionaryItemDTO getItemByItemTypeAndItemValue(String itemType, String itemValue) {
        return dictionaryItemRepository.findByItemTypeAndItemValue(itemType, itemValue).map(DictionaryItemConverter::convertToDTO).orElse(null);
    }

    @Override
    public DictionaryItemDTO addItem(DictionaryItemDTO item) {
        DictionaryItem itemEntity  = DictionaryItemConverter.convertToEntity(item);
        DictionaryItem dictionaryItem = dictionaryItemRepository.save(itemEntity);
        return DictionaryItemConverter.convertToDTO(dictionaryItem);
    }

    @Override
    public DictionaryItemDTO updateItem(Long id, DictionaryItemDTO item) {
        Optional<DictionaryItem> optionalExistingItem = dictionaryItemRepository.findById(id);
        if (optionalExistingItem.isPresent()) {
            DictionaryItem updateItem = optionalExistingItem.get();
            updateItem.setItemType(item.getItemType());
            updateItem.setItemName(item.getItemName());
            updateItem.setItemValue(item.getItemValue());
            updateItem.setDescription(item.getDescription());
            updateItem = dictionaryItemRepository.save(updateItem);
            return DictionaryItemConverter.convertToDTO(updateItem);
        }
        return null;
    }

    @Override
    public void deleteItem(Long id) {
        dictionaryItemRepository.deleteById(id);
    }

    public static DictionaryItemDTO convertToDTO(DictionaryItem item) {
        DictionaryItemDTO itemDTO = new DictionaryItemDTO();
        itemDTO.setId(item.getId());
        itemDTO.setItemName(item.getItemName());
        itemDTO.setItemType(item.getItemType());
        itemDTO.setItemValue(item.getItemValue());
        itemDTO.setDescription(item.getDescription());
        return itemDTO;
    }

    public static DictionaryItem convertToEntity(DictionaryItemDTO itemDTO) {
        DictionaryItem item = new DictionaryItem();
        item.setId(itemDTO.getId());
        item.setItemName(itemDTO.getItemName());
        item.setItemType(itemDTO.getItemType());
        item.setItemValue(itemDTO.getItemValue());
        item.setDescription(itemDTO.getDescription());
        return item;
    }

}
