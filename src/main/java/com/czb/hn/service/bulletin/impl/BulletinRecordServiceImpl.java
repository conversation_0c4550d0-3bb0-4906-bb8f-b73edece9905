package com.czb.hn.service.bulletin.impl;

import com.czb.hn.dto.bulletin.BulletinGenerationRecordDto;
import com.czb.hn.dto.bulletin.BulletinParams;
import com.czb.hn.dto.bulletin.BulletinPushRecordDto;
import com.czb.hn.dto.common.PageResult;
import com.czb.hn.enums.PushMethod;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;
import com.czb.hn.jpa.securadar.entity.BulletinPushRecordEntity;
import com.czb.hn.jpa.securadar.entity.JobEntity;
import com.czb.hn.jpa.securadar.repository.BulletinGenerationRecordRepository;
import com.czb.hn.jpa.securadar.repository.BulletinPushRecordRepository;
import com.czb.hn.jpa.securadar.repository.JobRepository;
import com.czb.hn.service.bulletin.BulletinRecordService;
import com.czb.hn.service.minio.MinioStorageService;
import com.czb.hn.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 简报记录服务实现类
 */
@Service
public class BulletinRecordServiceImpl implements BulletinRecordService {

    private static final Logger log = LoggerFactory.getLogger(BulletinRecordServiceImpl.class);

    private final BulletinGenerationRecordRepository generationRecordRepository;

    @Autowired
    private BulletinPushRecordRepository pushRecordRepository;

    @Autowired
    private MinioStorageService minioStorageService;

    @Autowired
    private JobRepository jobRepository;

    public BulletinRecordServiceImpl(BulletinGenerationRecordRepository generationRecordRepository) {
        this.generationRecordRepository = generationRecordRepository;
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> getGenerationRecords(
            LocalDateTime startTime,
            LocalDateTime endTime,
            int page,
            int size) {
        Assert.notNull(startTime, "开始时间不能为空");
        Assert.notNull(endTime, "结束时间不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage = generationRecordRepository.findByGenerationTimeBetween(
                startTime,
                endTime,
                pageRequest);

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public BulletinGenerationRecordDto getGenerationRecordById(Long id) {
        Assert.notNull(id, "记录ID不能为空");

        // 调用DAO层获取Entity
        Optional<BulletinGenerationRecordEntity> entityOpt = generationRecordRepository.findById(id);

        // 将Entity转换为DTO返回给Controller层
        return entityOpt.map(this::convertToGenerationDto).orElse(null);
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> getGenerationRecordsByJobId(
            Long jobId,
            int page,
            int size) {
        Assert.notNull(jobId, "任务ID不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage = generationRecordRepository.findByJobId(jobId, pageRequest);

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> getGenerationRecordsByType(
            String bulletinType,
            int page,
            int size) {
        Assert.hasText(bulletinType, "简报类型不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage = generationRecordRepository.findByBulletinType(
                bulletinType,
                pageRequest);

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public List<BulletinGenerationRecordDto> getRecentGenerationRecords(int limit) {
        Assert.isTrue(limit > 0, "限制数量必须大于0");

        PageRequest pageRequest = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取Entity列表
        List<BulletinGenerationRecordEntity> entities = generationRecordRepository.findRecentRecords(pageRequest);

        // 将Entity列表转换为DTO列表返回给Controller层
        return entities.stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<BulletinPushRecordDto> getPushRecordsByGenerationId(
            Long generationId,
            int page,
            int size) {
        Assert.notNull(generationId, "生成记录ID不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "pushTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinPushRecordEntity> entityPage = pushRecordRepository.findByGenerationId(generationId, pageRequest);

        // 将Entity转换为DTO
        List<BulletinPushRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToPushDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    @Transactional
    public List<Long> manualPushBulletin(Long generationId) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 调用DAO层获取Entity
        BulletinGenerationRecordEntity generationRecord = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        // 检查是否有内容可推送
        if (generationRecord.getFileObjectName() == null) {
            throw new IllegalArgumentException("生成记录没有内容可推送");
        }

        // 根据generationId查询关联任务信息，获取接收人列表
        Long jobId = generationRecord.getJobId();
        if (jobId == null) {
            throw new IllegalArgumentException("生成记录未关联任务ID");
        }
        JobEntity jobEntity = jobRepository.findById(jobId)
                .orElseThrow(() -> new IllegalArgumentException("找不到关联的任务: " + jobId));
        String jobParams = jobEntity.getJobParams();
        BulletinParams params = JsonUtil.fromJson(jobParams, BulletinParams.class);
        List<String> emailReceivers = (params != null && params.emailReceivers() != null) ? params.emailReceivers()
                : List.of();
        List<String> smsReceivers = (params != null && params.smsReceivers() != null) ? params.smsReceivers()
                : List.of();

        // 确保至少有一种接收方式
        if ((emailReceivers == null || emailReceivers.isEmpty()) &&
                (smsReceivers == null || smsReceivers.isEmpty())) {
            throw new IllegalArgumentException("必须至少配置一种接收方式（邮件或短信）");
        }

        List<Long> pushRecordIds = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 发送邮件
        if (emailReceivers != null && !emailReceivers.isEmpty()) {
            for (String email : emailReceivers) {
                // 创建Entity
                BulletinPushRecordEntity pushRecord = createPushRecordEntity(
                        generationId,
                        email,
                        PushType.EMAIL.name(),
                        PushMethod.MANUAL.name(),
                        now);

                // 调用DAO层保存Entity
                BulletinPushRecordEntity savedRecord = pushRecordRepository.save(pushRecord);
                pushRecordIds.add(savedRecord.getId());

                // 实际发送邮件的逻辑
                try {
                    // 获取预签名URL（有效期24小时）
                    String fileUrl = getBulletinFileUrl(generationId, 24 * 60 * 60);

                    // TODO: 调用邮件服务发送邮件
                    // String emailContent = "请通过以下链接查看简报：<br><a href='" + fileUrl +
                    // "'>点击下载简报</a><br>链接有效期24小时。";
                    // emailService.sendHtmlEmail(email, generationRecord.getBulletinTitle(),
                    // emailContent);

                    // 更新推送状态为成功
                    updatePushRecordStatus(savedRecord, PushStatus.SUCCESS);
                } catch (Exception e) {
                    log.error("发送邮件失败: {}", email, e);

                    // 更新推送状态为失败
                    updatePushRecordStatus(savedRecord, PushStatus.FAILURE);
                }
            }
        }

        // 发送短信
        if (smsReceivers != null && !smsReceivers.isEmpty()) {
            for (String phone : smsReceivers) {
                // 创建Entity
                BulletinPushRecordEntity pushRecord = createPushRecordEntity(
                        generationId,
                        phone,
                        PushType.SMS.name(),
                        PushMethod.MANUAL.name(),
                        now);

                // 调用DAO层保存Entity
                BulletinPushRecordEntity savedRecord = pushRecordRepository.save(pushRecord);
                pushRecordIds.add(savedRecord.getId());

                // 实际发送短信的逻辑
                try {
                    // TODO: 调用短信服务发送短信
                    // String smsContent = generationRecord.getBulletinTitle() + " 已生成，请登录系统查看详情。";
                    // smsService.sendSms(phone, smsContent);

                    // 更新推送状态为成功
                    updatePushRecordStatus(savedRecord, PushStatus.SUCCESS);
                } catch (Exception e) {
                    log.error("发送短信失败: {}", phone, e);

                    // 更新推送状态为失败
                    updatePushRecordStatus(savedRecord, PushStatus.FAILURE);
                }
            }
        }

        return pushRecordIds;
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> searchGenerationRecords(
            LocalDateTime startTime,
            LocalDateTime endTime,
            String bulletinType,
            int page,
            int size) {
        // 参数校验
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 如果未指定时间范围，默认查询最近30天的记录
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(30);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage = generationRecordRepository
                .findByGenerationTimeBetweenAndBulletinType(
                        startTime,
                        endTime,
                        (bulletinType != null && !bulletinType.isBlank()) ? bulletinType : null,
                        pageRequest);

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> getGenerationRecordsByPlanId(
            Long planId,
            int page,
            int size) {
        Assert.notNull(planId, "方案ID不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage = generationRecordRepository.findByPlanId(planId, pageRequest);

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public PageResult<BulletinGenerationRecordDto> searchGenerationRecordsByPlanId(
            Long planId,
            LocalDateTime startTime,
            LocalDateTime endTime,
            String bulletinType,
            int page,
            int size) {
        // 参数校验
        Assert.notNull(planId, "方案ID不能为空");
        Assert.isTrue(page >= 0, "页码不能小于0");
        Assert.isTrue(size > 0, "每页大小必须大于0");

        // 如果未指定时间范围，默认查询最近30天的记录
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(30);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "generationTime"));

        // 调用DAO层获取分页Entity数据
        Page<BulletinGenerationRecordEntity> entityPage;
        if (bulletinType != null && !bulletinType.isBlank()) {
            entityPage = generationRecordRepository.findByPlanIdAndGenerationTimeBetweenAndBulletinType(
                    planId, startTime, endTime, bulletinType, pageRequest);
        } else {
            entityPage = generationRecordRepository.findByPlanIdAndGenerationTimeBetween(
                    planId, startTime, endTime, pageRequest);
        }

        // 将Entity转换为DTO
        List<BulletinGenerationRecordDto> dtoList = entityPage.getContent().stream()
                .map(this::convertToGenerationDto)
                .collect(Collectors.toList());

        // 构建分页结果
        return new PageResult<>(
                dtoList,
                entityPage.getTotalElements(),
                entityPage.getTotalPages(),
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.isFirst(),
                entityPage.isLast(),
                entityPage.isEmpty());
    }

    @Override
    public byte[] getBulletinContent(Long generationId) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 从数据库获取文件对象名
        BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        if (record.getFileObjectName() == null) {
            throw new IllegalStateException("简报文件不存在");
        }

        // 从MinIO下载文件
        return minioStorageService.downloadFile(record.getFileObjectName());
    }

    @Override
    public String getBulletinFileUrl(Long generationId, int expirySeconds) {
        Assert.notNull(generationId, "生成记录ID不能为空");

        // 从数据库获取文件对象名
        BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        if (record.getFileObjectName() == null) {
            throw new IllegalStateException("简报文件不存在");
        }

        // 生成预签名URL
        return minioStorageService.getPresignedUrl(record.getFileObjectName(), expirySeconds);
    }

    @Override
    @Transactional
    public void saveBulletinContent(Long generationId, byte[] content, String filename) {
        Assert.notNull(generationId, "生成记录ID不能为空");
        Assert.notNull(content, "简报内容不能为空");
        Assert.hasText(filename, "文件名不能为空");

        // 从数据库获取生成记录
        BulletinGenerationRecordEntity record = generationRecordRepository.findById(generationId)
                .orElseThrow(() -> new IllegalArgumentException("找不到指定的生成记录: " + generationId));

        // 上传到MinIO
        String objectName = minioStorageService.uploadFile(content, filename, "application/pdf");

        // 更新生成记录
        record.setFileObjectName(objectName);
        record.setFileSize((long) content.length);
        record.setStatus("SUCCESS");
        record.setUpdatedAt(LocalDateTime.now());

        generationRecordRepository.save(record);

        log.info("简报内容保存成功，生成记录ID: {}, 文件对象名: {}", generationId, objectName);
    }

    @Override
    @Transactional
    public void deleteGenerationRecord(Long id) {
        Assert.notNull(id, "生成记录ID不能为空");
        generationRecordRepository.logicDeleteById(id);
    }

    @Override
    @Transactional
    public void deleteGenerationRecords(List<Long> ids) {
        Assert.notEmpty(ids, "ID集合不能为空");
        generationRecordRepository.logicDeleteByIdIn(ids);
    }

    @Override
    public byte[] batchDownloadBulletins(List<Long> ids) {
        Assert.notEmpty(ids, "ID集合不能为空");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            for (Long id : ids) {
                BulletinGenerationRecordEntity record = generationRecordRepository.findById(id)
                        .orElse(null);
                if (record == null || Boolean.TRUE.equals(record.getIsDeleted())) {
                    continue;
                }
                String fileObjectName = record.getFileObjectName();
                if (fileObjectName == null) {
                    continue;
                }
                byte[] fileBytes;
                try {
                    fileBytes = minioStorageService.downloadFile(fileObjectName);
                } catch (Exception e) {
                    continue;
                }
                String fileName = (record.getBulletinTitle() != null ? record.getBulletinTitle() : ("bulletin_" + id))
                        + ".pdf";
                ZipEntry entry = new ZipEntry(fileName);
                zos.putNextEntry(entry);
                zos.write(fileBytes);
                zos.closeEntry();
            }
            zos.finish();
        } catch (IOException e) {
            throw new RuntimeException("压缩文件生成失败: " + e.getMessage(), e);
        }
        return baos.toByteArray();
    }

    /**
     * 将生成记录实体转换为DTO
     */
    private BulletinGenerationRecordDto convertToGenerationDto(BulletinGenerationRecordEntity entity) {
        // 检查是否可以推送（有文件且状态正常）
        boolean canPush = entity.getFileObjectName() != null && "SUCCESS".equals(entity.getStatus());

        return new BulletinGenerationRecordDto(
                entity.getId(),
                entity.getBulletinTitle(),
                entity.getBulletinType(),
                entity.getStartTime(),
                entity.getEndTime(),
                entity.getGenerationTime(),
                entity.getJobId(),
                entity.getPlanId(),
                canPush,
                entity.getStatus());
    }

    /**
     * 创建推送记录实体
     */
    private BulletinPushRecordEntity createPushRecordEntity(Long generationId, String account, String pushType,
            String pushMethod, LocalDateTime now) {
        BulletinPushRecordEntity pushRecord = new BulletinPushRecordEntity();
        pushRecord.setGenerationId(generationId);
        pushRecord.setAccount(account);
        pushRecord.setPushType(pushType);
        pushRecord.setPushMethod(pushMethod);
        pushRecord.setPushTime(now);
        pushRecord.setStatus(PushStatus.PENDING.name());
        pushRecord.setCreatedAt(now);
        pushRecord.setUpdatedAt(now);
        return pushRecord;
    }

    /**
     * 更新推送记录状态
     */
    private void updatePushRecordStatus(BulletinPushRecordEntity pushRecord, PushStatus status) {
        pushRecord.setStatus(status.name());
        pushRecord.setUpdatedAt(LocalDateTime.now());
        pushRecordRepository.save(pushRecord);
    }

    /**
     * 将推送记录实体转换为DTO
     */
    private BulletinPushRecordDto convertToPushDto(BulletinPushRecordEntity entity) {
        return new BulletinPushRecordDto(
                entity.getId(),
                entity.getGenerationId(),
                entity.getAccount(),
                entity.getPushType(),
                entity.getPushMethod(),
                entity.getPushTime(),
                entity.getStatus());
    }
}