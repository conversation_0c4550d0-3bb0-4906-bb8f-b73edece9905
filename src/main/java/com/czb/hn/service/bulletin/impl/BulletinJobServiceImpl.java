package com.czb.hn.service.bulletin.impl;

import com.czb.hn.dto.bulletin.BulletinJobRequest;
import com.czb.hn.dto.bulletin.BulletinParams;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import com.czb.hn.jpa.securadar.entity.BriefingConfiguration;
import com.czb.hn.jpa.securadar.entity.JobEntity;
import com.czb.hn.jpa.securadar.repository.BriefingConfigurationRepository;
import com.czb.hn.jpa.securadar.repository.JobRepository;
import com.czb.hn.service.job.JobService;
import com.czb.hn.service.bulletin.BulletinJobService;
import com.czb.hn.util.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.lang.reflect.Method;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 简报任务服务实现类
 */
@Service
public class BulletinJobServiceImpl implements BulletinJobService {

    private static final Logger log = LoggerFactory.getLogger(BulletinJobServiceImpl.class);
    
    private final JobService jobService;
    
    private final JobRepository jobRepository;

    private final BriefingConfigurationRepository briefingConfigurationRepository;
    
    private final ObjectMapper objectMapper;

    public BulletinJobServiceImpl(
            JobService jobService, 
            JobRepository jobRepository, 
            BriefingConfigurationRepository briefingConfigurationRepository,
            ObjectMapper objectMapper) {
        this.jobService = jobService;
        this.jobRepository = jobRepository;
        this.briefingConfigurationRepository = briefingConfigurationRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional
    public void createBulletinConfiguration(Long planId) {
        log.info("开始为方案ID: {} 创建简报配置任务", planId);
        Assert.notNull(planId, "方案id不可为空");

        // 查找简报配置
        BriefingConfiguration config = briefingConfigurationRepository.findByPlanId(planId);
        if (config == null) {
            log.error("找不到方案ID对应的简报配置: {}", planId);
            throw new IllegalArgumentException("找不到方案ID对应的简报配置: " + planId);
        }

        try {
            // 删除现有任务
            int deletedCount = deleteExistingBulletinJobs(planId);
            log.info("已删除方案ID: {} 的{}个已有简报任务", planId, deletedCount);
            
            // 获取方案名称
            String planName = config.getName();
            log.info("方案名称: {}", planName);
            
            List<Long> createdJobIds = new ArrayList<>();
            
            // 创建各类型简报任务
            createDailyBulletinJob(config, planId, planName, createdJobIds);
            createWeeklyBulletinJob(config, planId, planName, createdJobIds);
            createMonthlyBulletinJob(config, planId, planName, createdJobIds);
            
            log.info("方案ID: {} 成功创建简报任务，任务ID列表: {}", planId, createdJobIds);
        } catch (Exception e) {
            log.error("创建简报配置任务失败，方案ID: {}, 错误: {}", planId, e.getMessage(), e);
            throw new RuntimeException("创建简报配置任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建日报任务
     */
    private void createDailyBulletinJob(BriefingConfiguration config, Long planId, String planName, List<Long> createdJobIds) {
        Boolean dailyActive =  config.getDailyBriefingIsActive();

        if (!Boolean.TRUE.equals(dailyActive)) {
            log.info("方案: {} 的日报功能未启用，跳过创建", planName);
            return;
        }
        
        try {
            // 解析日报接收设置
            String dailySettingsJson = config.getDailyReceptionSettings();
            BriefingReceptionSettingsDto dailySettings = parseReceptionSettings(dailySettingsJson);
            String contentSettings = config.getContentSettings();

            // 验证接收时间格式
            String receptionTime = dailySettings.receptionTime();
            validateTimeFormat(receptionTime, "日报接收时间");
            
            // 获取邮件和短信接收人
            List<String> emailReceivers = extractEmailReceivers(dailySettings);
            List<String> smsReceivers = extractSmsReceivers(dailySettings);
            
            // 验证至少有一种接收方式
            validateReceivers(emailReceivers, smsReceivers, "日报");
            
            // 创建日报任务
            BulletinJobRequest dailyRequest = new BulletinJobRequest(
                    null, // 新建任务，ID为null
                    planId,
                    planName + "-日报", // 任务名称
                    "DAILY", // 简报类型
                    receptionTime, // 发送时间
                    null, // 日报不需要执行日
                    true, // 启用
                    planName + "日报", // 简报标题
                    emailReceivers, // 邮件接收人
                    smsReceivers, // 短信接收人
                    contentSettings,
                    null // 组织ID
            );
            
            log.info("准备创建日报任务，计划名称: {}, 接收时间: {}, 邮件接收人数: {}, 短信接收人数: {}", 
                    planName, receptionTime, emailReceivers.size(), smsReceivers.size());
                    
            Long dailyJobId = saveBulletinJobInternal(dailyRequest, planId);
            createdJobIds.add(dailyJobId);
            log.info("日报任务创建成功，任务ID: {}", dailyJobId);
        } catch (Exception e) {
            log.error("创建日报任务失败，方案名称: {}, 错误: {}", planName, e.getMessage(), e);
            throw new RuntimeException("创建日报任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建周报任务
     */
    private void createWeeklyBulletinJob(BriefingConfiguration config, Long planId, String planName, List<Long> createdJobIds) {
        Boolean weeklyActive = config.getWeeklyBriefingIsActive();
        if (!Boolean.TRUE.equals(weeklyActive)) {
            log.info("方案: {} 的周报功能未启用，跳过创建", planName);
            return;
        }
        
        try {
            // 解析周报接收设置
            String weeklySettingsJson = config.getWeeklyReceptionSettings();
            BriefingReceptionSettingsDto weeklySettings = parseReceptionSettings(weeklySettingsJson);
            String contentSettings = config.getContentSettings();
            // 验证接收时间格式
            String receptionTime = weeklySettings.receptionTime();
            validateTimeFormat(receptionTime, "周报接收时间");
            
            // 设置执行日（默认周一）
            String executionDay = "1"; // 1-7 代表周一到周日
            validateExecutionDay(executionDay, 1, 7, "周报执行日");
            
            // 获取邮件和短信接收人
            List<String> emailReceivers = extractEmailReceivers(weeklySettings);
            List<String> smsReceivers = extractSmsReceivers(weeklySettings);
            
            // 验证至少有一种接收方式
            validateReceivers(emailReceivers, smsReceivers, "周报");
            
            // 创建周报任务
            BulletinJobRequest weeklyRequest = new BulletinJobRequest(
                    null, // 新建任务，ID为null
                    planId,
                    planName + "-周报", // 任务名称
                    "WEEKLY", // 简报类型
                    receptionTime, // 发送时间
                    executionDay, // 执行日为周一
                    true, // 启用
                    planName + "周报", // 简报标题
                    emailReceivers, // 邮件接收人
                    smsReceivers, // 短信接收人
                    contentSettings,
                    null // 组织ID
            );
            
            log.info("准备创建周报任务，计划名称: {}, 接收时间: {}, 执行日: {}, 邮件接收人数: {}, 短信接收人数: {}", 
                    planName, receptionTime, executionDay, emailReceivers.size(), smsReceivers.size());
            
            Long weeklyJobId = saveBulletinJobInternal(weeklyRequest, planId);
            createdJobIds.add(weeklyJobId);
            log.info("周报任务创建成功，任务ID: {}", weeklyJobId);
        } catch (Exception e) {
            log.error("创建周报任务失败，方案名称: {}, 错误: {}", planName, e.getMessage(), e);
            throw new RuntimeException("创建周报任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建月报任务
     */
    private void createMonthlyBulletinJob(BriefingConfiguration config, Long planId, String planName, List<Long> createdJobIds) {
        Boolean monthlyActive = config.getMonthlyBriefingIsActive();
        if (!Boolean.TRUE.equals(monthlyActive)) {
            log.info("方案: {} 的月报功能未启用，跳过创建", planName);
            return;
        }
        
        try {
            // 解析月报接收设置
            String monthlySettingsJson = config.getMonthlyReceptionSettings();
            BriefingReceptionSettingsDto monthlySettings = parseReceptionSettings(monthlySettingsJson);
            String contentSettings = config.getContentSettings();
            // 验证接收时间格式
            String receptionTime = monthlySettings.receptionTime();
            validateTimeFormat(receptionTime, "月报接收时间");
            
            // 设置执行日（默认每月1号）
            String executionDay = "1"; // 1-31 代表每月的日期
            validateExecutionDay(executionDay, 1, 31, "月报执行日");
            
            // 获取邮件和短信接收人
            List<String> emailReceivers = extractEmailReceivers(monthlySettings);
            List<String> smsReceivers = extractSmsReceivers(monthlySettings);
            
            // 验证至少有一种接收方式
            validateReceivers(emailReceivers, smsReceivers, "月报");
            
            // 创建月报任务
            BulletinJobRequest monthlyRequest = new BulletinJobRequest(
                    null, // 新建任务，ID为null
                    planId,
                    planName + "-月报", // 任务名称
                    "MONTHLY", // 简报类型
                    receptionTime, // 发送时间
                    executionDay, // 执行日为每月1号
                    true, // 启用
                    planName + "月报", // 简报标题
                    emailReceivers, // 邮件接收人
                    smsReceivers, // 短信接收人
                    contentSettings,
                    null // 组织ID
            );
            
            log.info("准备创建月报任务，计划名称: {}, 接收时间: {}, 执行日: {}, 邮件接收人数: {}, 短信接收人数: {}", 
                    planName, receptionTime, executionDay, emailReceivers.size(), smsReceivers.size());
            
            Long monthlyJobId = saveBulletinJobInternal(monthlyRequest, planId);
            createdJobIds.add(monthlyJobId);
            log.info("月报任务创建成功，任务ID: {}", monthlyJobId);
        } catch (Exception e) {
            log.error("创建月报任务失败，方案名称: {}, 错误: {}", planName, e.getMessage(), e);
            throw new RuntimeException("创建月报任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证时间格式是否有效（HH:mm）
     */
    private void validateTimeFormat(String timeStr, String fieldName) {
        if (timeStr == null || !timeStr.matches("^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")) {
            throw new IllegalArgumentException(fieldName + "格式无效，应为HH:mm格式，如: 09:30");
        }
    }
    
    /**
     * 验证执行日是否在有效范围内
     */
    private void validateExecutionDay(String executionDay, int min, int max, String fieldName) {
        try {
            int day = Integer.parseInt(executionDay);
            if (day < min || day > max) {
                throw new IllegalArgumentException(fieldName + "应在" + min + "-" + max + "范围内");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(fieldName + "必须为有效数字");
        }
    }
    
    /**
     * 验证是否至少有一种接收方式
     */
    private void validateReceivers(List<String> emailReceivers, List<String> smsReceivers, String bulletinType) {
        if ((emailReceivers == null || emailReceivers.isEmpty()) && 
            (smsReceivers == null || smsReceivers.isEmpty())) {
            throw new IllegalArgumentException(bulletinType + "必须至少配置一种接收方式（邮件或短信）");
        }
    }
    
    /**
     * 删除方案下的现有简报任务
     * @return 删除的任务数量
     */
    private int deleteExistingBulletinJobs(Long planId) {
        int deletedCount = 0;
        List<JobEntity> existingJobs = jobService.getJobsByPlanId(planId);
        
        for (JobEntity job : existingJobs) {
            try {
                String name = job.getName();
                
                if (name != null && 
                    (name.endsWith("-日报-发送") || 
                     name.endsWith("-周报-发送") || 
                     name.endsWith("-月报-发送") ||
                     name.endsWith("-日报-生成") || 
                     name.endsWith("-周报-生成") || 
                     name.endsWith("-月报-生成"))) {
                    
                    Long id = job.getId();
                    jobService.deleteJob(id);
                    log.debug("已删除简报任务: {}, ID: {}", name, id);
                    deletedCount++;
                }
            } catch (Exception e) {
                log.warn("删除简报任务时发生错误: {}", e.getMessage());
            }
        }
        return deletedCount;
    }

    @Override
    @Transactional
    public Long saveBulletinJob(BulletinJobRequest request) {
        Assert.notNull(request, "简报任务请求不能为空");
        
        // 从请求中尝试提取方案ID
        Long planId = extractPlanIdFromJobName(request.name());
        if (planId == null) {
            throw new IllegalArgumentException("无法从任务名称提取方案ID，请确保任务名称格式正确");
        }
        
        return saveBulletinJobInternal(request, planId);
    }
    
    /**
     * 内部使用的保存任务方法，接收planId参数
     */
    private Long saveBulletinJobInternal(BulletinJobRequest request, Long planId) {
        Assert.notNull(request, "简报任务请求不能为空");
        Assert.notNull(planId, "方案ID不能为空");
        
        try {
            String bulletinType = request.bulletinType();
            String sendTime = request.sendTime();
            String executionDay = request.executionDay();
            String planName = request.name().substring(0, request.name().lastIndexOf("-"));
            
            // 1. 先创建简报生成任务（每日凌晨2点自动生成当日简报内容）
            Long generatorJobId = createGeneratorJob(request);
            log.info("成功创建简报生成任务，ID: {}, 类型: {}", generatorJobId, bulletinType);
            
            // 2. 再创建简报发送任务（按照设定的时间发送简报）
            Long senderJobId = createSenderJob(request);
            log.info("成功创建简报发送任务，ID: {}, 类型: {}", senderJobId, bulletinType);
            
            return senderJobId;
        } catch (Exception e) {
            log.error("保存简报任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存简报任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void enableBulletinJob(Long id) {
        jobService.enableJob(id);
        
        // 同时启用对应的生成任务
        JobEntity senderJob = jobRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("任务不存在: " + id));
        
        String baseName = senderJob.getName().replace("-发送", "");
        String generatorName = baseName + "-生成";
        
        // 查找所有任务
        List<JobEntity> allJobs = jobService.getAllJobs();
        
        // 找到对应的生成任务
        allJobs.stream()
            .filter(job -> job.getName().equals(generatorName))
            .findFirst()
            .ifPresent(generatorJob -> jobService.enableJob(generatorJob.getId()));
    }

    @Override
    @Transactional
    public void disableBulletinJob(Long id) {
        jobService.disableJob(id);
        
        // 同时禁用对应的生成任务
        JobEntity senderJob = jobRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("任务不存在: " + id));
        
        String baseName = senderJob.getName().replace("-发送", "");
        String generatorName = baseName + "-生成";
        
        // 查找所有任务
        List<JobEntity> allJobs = jobService.getAllJobs();
        
        // 找到对应的生成任务
        allJobs.stream()
            .filter(job -> job.getName().equals(generatorName))
            .findFirst()
            .ifPresent(generatorJob -> jobService.disableJob(generatorJob.getId()));
    }

    @Override
    @Transactional
    public void deleteBulletinJob(Long id) {
        // 查找关联的生成任务（命名规则：xxx-发送的关联任务是xxx-生成）
        JobEntity senderJob = jobRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("任务不存在: " + id));
        
        String baseName = senderJob.getName().replace("-发送", "");
        String generatorName = baseName + "-生成";
        
        // 查找所有任务
        List<JobEntity> allJobs = jobService.getAllJobs();
        
        // 找到对应的生成任务
        allJobs.stream()
            .filter(job -> job.getName().equals(generatorName))
            .findFirst()
            .ifPresent(generatorJob -> jobService.deleteJob(generatorJob.getId()));
        
        // 删除发送任务
        jobService.deleteJob(id);
    }
    
    @Override
    public List<BulletinJobRequest> getBulletinJobsByPlanId(Long planId) {
        // 获取方案下所有任务
        List<JobEntity> jobs = jobService.getJobsByPlanId(planId);
        
        // 过滤出发送任务（以"-发送"结尾的任务）
        List<JobEntity> senderJobs = new ArrayList<>();
        for (JobEntity job : jobs) {
            try {
                String name = job.getName();
                if (name != null && name.endsWith("-发送")) {
                    senderJobs.add(job);
                }
            } catch (Exception e) {
                throw new RuntimeException("获取任务名称失败", e);
            }
        }
        
        // 将任务实体转换为请求DTO
        return senderJobs.stream()
            .map(this::convertToRequest)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public List<Long> updateBulletinJobsByPlanId(Long planId, List<BulletinJobRequest> requests) {
        // 获取方案下现有的所有任务
        List<JobEntity> existingJobs = jobService.getJobsByPlanId(planId);
        
        // 创建名称到任务的映射
        Map<String, JobEntity> existingSenderJobMap = new HashMap<>();
        for (JobEntity job : existingJobs) {
            try {
                String name = job.getName();
                if (name != null && name.endsWith("-发送")) {
                    existingSenderJobMap.put(name, job);
                }
            } catch (Exception e) {
                throw new RuntimeException("无法获取任务名称", e);
            }
        }
        
        // 处理新任务列表
        List<Long> updatedJobIds = new ArrayList<>();
        
        for (BulletinJobRequest request : requests) {
            Long jobId;
            String senderJobName = request.name() + "-发送";
            
            if (existingSenderJobMap.containsKey(senderJobName)) {
                // 更新现有任务
                JobEntity existingJob = existingSenderJobMap.get(senderJobName);
                try {
                    Long existingJobId = existingJob.getId();
                    request = new BulletinJobRequest(
                            existingJobId,  // 使用现有任务的ID
                            planId,
                            request.name(),
                            request.bulletinType(),
                            request.sendTime(),
                            request.executionDay(),
                            request.enabled(),
                            request.bulletinTitle(),
                            request.emailReceivers(),
                            request.smsReceivers(),
                            request.params(),
                            request.groupIds()
                    );
                    updateExistingJob(request);
                    jobId = existingJobId;
                    existingSenderJobMap.remove(senderJobName);
                } catch (Exception e) {
                    throw new RuntimeException("无法更新任务", e);
                }
            } else {
                // 创建新任务
                jobId = saveBulletinJobInternal(request, planId);
            }
            
            updatedJobIds.add(jobId);
        }
        
        // 删除未在新列表中的旧任务
        for (JobEntity jobToDelete : existingSenderJobMap.values()) {
            try {
                Long jobId = jobToDelete.getId();
                deleteBulletinJob(jobId);
            } catch (Exception e) {
                throw new RuntimeException("无法删除任务", e);
            }
        }
        
        return updatedJobIds;
    }
    
    /**
     * 更新现有任务
     */
    private void updateExistingJob(BulletinJobRequest request) {
        try {
            // 获取发送任务
            JobEntity senderJob = jobRepository.findById(request.id())
                .orElseThrow(() -> new RuntimeException("任务不存在: " + request.id()));
            
            // 更新发送任务基本属性
            senderJob.setName(request.name() + "-发送");
            senderJob.setEnabled(request.enabled());
            senderJob.setCycleType(request.bulletinType());
            senderJob.setExecutionDay(request.executionDay());
            senderJob.setCron(generateCronExpression(request, false));
            senderJob.setHandler("bulletinSenderJob#send");
            
            // 更新首次生效时间 - 解决更新任务时没有更新beginTime的问题
            setFirstEffectiveTime(senderJob, request, false);
            
            // 更新任务参数
            BulletinParams senderParams = new BulletinParams(
                request.bulletinTitle(),
                request.emailReceivers(),
                request.smsReceivers(),
                request.params(),
                true, // 只在非工作日发送
                true, // 需要补发
                null, // 保持最后发送日期不变
                request.groupIds(),
                null // 发送任务不需要数据范围
            );
            senderJob.setJobParams(JsonUtil.toJson(senderParams));
            
            // 保存发送任务
            jobService.updateJob(senderJob);
            
            // 查找并更新生成任务
            String baseName = senderJob.getName();
            baseName = baseName.replace("-发送", "");
            
            // 查找所有任务
            List<JobEntity> allJobs = jobService.getAllJobs();
            
            // 找到对应的生成任务
            for (JobEntity job : allJobs) {
                String jobName = job.getName();
                if (jobName != null && jobName.equals(baseName + "-生成")) {
                    // 更新生成任务基本属性
                    job.setName(request.name() + "-生成");
                    job.setEnabled(request.enabled());
                    job.setCycleType(request.bulletinType());
                    job.setExecutionDay(request.executionDay());
                    job.setCron(generateCronExpression(request, true));
                    job.setHandler("bulletinGeneratorJob#generate");
                    
                    // 更新首次生效时间 - 解决更新任务时没有更新beginTime的问题
                    setFirstEffectiveTime(job, request, true);
                    
                    // 更新任务参数
                    BulletinParams generatorParams = new BulletinParams(
                        request.bulletinTitle(),
                        request.emailReceivers(),
                        request.smsReceivers(),
                        request.params(),
                        true, // 只在非工作日发送
                        true, // 需要补发
                        null, // 保持最后发送日期不变
                        request.groupIds(),
                        getDataRange(request.bulletinType())
                    );
                    job.setJobParams(JsonUtil.toJson(generatorParams));
                    
                    // 保存生成任务
                    jobService.updateJob(job);
                    break;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("更新任务失败", e);
        }
    }
    
    /**
     * 创建简报生成任务（凌晨执行）
     */
    private Long createGeneratorJob(BulletinJobRequest request) {
        try {
            // 创建任务
            JobEntity jobEntity = new JobEntity();
            
            // 设置任务属性
            jobEntity.setName(request.name() + "-生成");
            jobEntity.setEnabled(request.enabled());
            jobEntity.setCycleType(request.bulletinType());
            jobEntity.setExecutionDay(request.executionDay());
            jobEntity.setCron(generateCronExpression(request, true));
            jobEntity.setHandler("bulletinGeneratorJob#generate");
            jobEntity.setPlanId(request.planId());
            // 设置首次生效时间
            setFirstEffectiveTime(jobEntity, request, true);
            
            // 构建任务参数
            BulletinParams jobParams = new BulletinParams(
                request.bulletinTitle(),
                request.emailReceivers(),
                request.smsReceivers(),
                request.params(),
                true, // 只在非工作日发送
                true, // 需要补发
                null, // 首次没有最后发送日期
                request.groupIds(),
                getDataRange(request.bulletinType())
            );
            
            // 设置任务参数
            jobEntity.setJobParams(JsonUtil.toJson(jobParams));
            
            // 保存任务
            JobEntity savedEntity = jobService.createJob(jobEntity);
            
            // 获取ID
            return savedEntity.getId();
        } catch (Exception e) {
            throw new RuntimeException("创建生成任务失败", e);
        }
    }
    
    /**
     * 创建简报发送任务（配置时间执行）
     */
    private Long createSenderJob(BulletinJobRequest request) {
        try {
            // 创建任务
            JobEntity jobEntity = new JobEntity();
            
            // 设置任务属性
            jobEntity.setName(request.name() + "-发送");
            jobEntity.setEnabled(request.enabled());
            jobEntity.setCycleType(request.bulletinType());
            jobEntity.setExecutionDay(request.executionDay());
            jobEntity.setPlanId(request.planId());
            // 根据配置的发送时间生成cron表达式
            String cron = generateCronExpression(request, false);
            jobEntity.setCron(cron);
            
            // 设置任务处理器
            jobEntity.setHandler("bulletinSenderJob#send");
            
            // 设置首次生效时间
            setFirstEffectiveTime(jobEntity, request, false);
            
            // 构建任务参数
            BulletinParams jobParams = new BulletinParams(
                request.bulletinTitle(),
                request.emailReceivers(),
                request.smsReceivers(),
                request.params(),
                true, // 只在非工作日发送
                true, // 需要补发
                null, // 首次没有最后发送日期
                request.groupIds(),
                null // 发送任务不需要数据范围
            );
            
            // 设置任务参数
            jobEntity.setJobParams(JsonUtil.toJson(jobParams));
            
            // 保存任务
            JobEntity savedEntity = jobService.createJob(jobEntity);
            
            // 获取ID
            return savedEntity.getId();
        } catch (Exception e) {
            throw new RuntimeException("创建发送任务失败", e);
        }
    }

    
    /**
     * 将星期数映射为Cron表达式中的值
     */
    private String mapWeekdayToCron(String weekday) {
        // Cron表达式中: 1=SUN, 2=MON, ..., 7=SAT
        // 输入格式中: 1=MON, 2=TUE, ..., 7=SUN
        switch (weekday) {
            case "1": return "2"; // 周一
            case "2": return "3"; // 周二
            case "3": return "4"; // 周三
            case "4": return "5"; // 周四
            case "5": return "6"; // 周五
            case "6": return "7"; // 周六
            case "7": return "1"; // 周日
            default: return "2";  // 默认周一
        }
    }
    /**
     * 使用反射获取配置对象的属性值
     */
    private <T> T getConfigProperty(BriefingConfiguration config, String methodName, Class<T> returnType) {
        try {
            Method method = config.getClass().getMethod(methodName);
            return returnType.cast(method.invoke(config));
        } catch (Exception e) {
            log.error("获取配置属性失败: " + methodName, e);
            throw new RuntimeException("获取配置属性失败: " + methodName, e);
        }
    }
    
    /**
     * 从JSON字符串解析接收设置
     */
    private BriefingReceptionSettingsDto parseReceptionSettings(String settingsJson) {
        try {
            return objectMapper.readValue(settingsJson, BriefingReceptionSettingsDto.class);
        } catch (Exception e) {
            log.error("解析接收设置失败", e);
            throw new RuntimeException("解析接收设置失败", e);
        }
    }
    
    /**
     * 提取邮件接收人
     */
    private List<String> extractEmailReceivers(BriefingReceptionSettingsDto settings) {
        if (settings == null || 
            settings.receptionMethods() == null || 
            settings.receptionMethods().email() == null || 
            !Boolean.TRUE.equals(settings.receptionMethods().email().enabled()) ||
            settings.receptionMethods().email().recipients() == null) {
            return List.of();
        }
        
        return settings.receptionMethods().email().recipients().stream()
            .map(recipient -> {
                try {
                    // 使用反射获取email方法
                    Method emailMethod = recipient.getClass().getMethod("email");
                    return (String) emailMethod.invoke(recipient);
                } catch (Exception e) {
                    log.error("获取邮件地址失败", e);
                    return "";
                }
            })
            .filter(email -> !email.isEmpty())
            .collect(Collectors.toList());
    }
    
    /**
     * 提取短信接收人
     */
    private List<String> extractSmsReceivers(BriefingReceptionSettingsDto settings) {
        if (settings == null || 
            settings.receptionMethods() == null || 
            settings.receptionMethods().sms() == null ||
            !Boolean.TRUE.equals(settings.receptionMethods().sms().enabled()) ||
            settings.receptionMethods().sms().recipients() == null) {
            return List.of();
        }
        
        return settings.receptionMethods().sms().recipients().stream()
            .map(recipient -> {
                try {
                    // 使用反射获取phoneNumber方法
                    Method phoneMethod = recipient.getClass().getMethod("phoneNumber");
                    return (String) phoneMethod.invoke(recipient);
                } catch (Exception e) {
                    log.error("获取手机号码失败", e);
                    return "";
                }
            })
            .filter(phone -> !phone.isEmpty())
            .collect(Collectors.toList());
    }


    /**
     * 从任务名称中提取方案ID
     * 任务名称格式通常为：方案名称-报表类型
     */
    private Long extractPlanIdFromJobName(String jobName) {
        try {
            // 任务名称可能是 "方案A-日报"，我们需要提取"方案A"部分
            String planName = jobName.substring(0, jobName.lastIndexOf("-"));
            
            // 查找所有简报配置
            List<BriefingConfiguration> configs = briefingConfigurationRepository.findAll();
            
            // 遍历配置，找到匹配的方案名称
            for (BriefingConfiguration config : configs) {
                String configPlanName = getConfigProperty(config, "getName", String.class);
                if (planName.equals(configPlanName)) {
                    return getConfigProperty(config, "getPlanId", Long.class);
                }
            }
            
            // 如果找不到匹配的方案，尝试直接从名称中提取数字（如果名称包含方案ID）
            String[] parts = planName.split("\\D+");
            for (String part : parts) {
                if (!part.isEmpty()) {
                    try {
                        return Long.parseLong(part);
                    } catch (NumberFormatException e) {
                        // 忽略非数字部分
                    }
                }
            }
            
            return null;
        } catch (Exception e) {
            log.warn("从任务名称提取方案ID失败: {}", jobName, e);
            return null;
        }
    }
    
    /**
     * 获取数据范围（天数）
     * 返回从当前日期向前计算的实际天数
     */
    private Integer getDataRange(String bulletinType) {
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        
        switch (bulletinType) {
            case "DAILY":
                // 日报：前一天的数据
                startDate = today.minusDays(1);
                break;
            case "WEEKLY":
                // 周报：前一周的数据（7天）
                startDate = today.minusDays(7);
                break;
            case "MONTHLY":
                // 月报：前一个月的数据（根据实际月份天数计算）
                startDate = today.minusMonths(1);
                break;
            default:
                return 1;
        }
        
        // 计算日期差值（包含起始日期和结束日期）
        return (int) ChronoUnit.DAYS.between(startDate, today) + 1;
    }

    /**
     * 设置首次生效时间
     * @param jobEntity 任务实体
     * @param request 任务请求
     * @param isGenerator 是否为生成任务
     */
    private void setFirstEffectiveTime(JobEntity jobEntity, BulletinJobRequest request, boolean isGenerator) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime effectiveTime;
            
            if (isGenerator) {
                // 生成任务固定凌晨2点执行，而不是使用用户配置的发送时间
                LocalTime generatorTime = LocalTime.of(2, 0); // 凌晨2点
                
                if ("DAILY".equals(request.bulletinType())) {
                    // 日报：从今天开始，凌晨2点生成
                    effectiveTime = now.with(generatorTime);
                    // 如果当前时间已经过了凌晨2点，则从明天开始
                    if (now.isAfter(effectiveTime)) {
                        effectiveTime = effectiveTime.plusDays(1);
                    }
                } else if ("WEEKLY".equals(request.bulletinType())) {
                    // 周报：本周或下周指定执行日的凌晨2点
                    DayOfWeek targetDay = DayOfWeek.of(Integer.parseInt(request.executionDay()));
                    effectiveTime = now.with(TemporalAdjusters.nextOrSame(targetDay)).with(generatorTime);
                    // 如果当前日期就是执行日但已经过了凌晨2点，则设为下周
                    if (now.getDayOfWeek() == targetDay && now.isAfter(effectiveTime)) {
                        effectiveTime = effectiveTime.plusWeeks(1);
                    }
                } else if ("MONTHLY".equals(request.bulletinType())) {
                    // 月报：本月或下月指定日期的凌晨2点
                    int dayOfMonth = Integer.parseInt(request.executionDay());
                    // 处理当月或下月
                    LocalDateTime thisMonth = now.withDayOfMonth(
                        Math.min(dayOfMonth, now.toLocalDate().lengthOfMonth())
                    ).with(generatorTime);
                    
                    if (now.isAfter(thisMonth)) {
                        // 如果已经过了本月执行日期，则设为下月
                        effectiveTime = now.plusMonths(1)
                            .withDayOfMonth(Math.min(dayOfMonth, now.plusMonths(1).toLocalDate().lengthOfMonth()))
                            .with(generatorTime);
                    } else {
                        effectiveTime = thisMonth;
                    }
                } else {
                    // 默认今天或明天凌晨2点
                    effectiveTime = now.with(generatorTime);
                    if (now.isAfter(effectiveTime)) {
                        effectiveTime = effectiveTime.plusDays(1);
                    }
                }
            } else {
                // 发送任务使用用户配置的时间
                LocalTime sendTime = LocalTime.parse(request.sendTime());
                
                if ("DAILY".equals(request.bulletinType())) {
                    // 日报：今天或明天指定时间
                    effectiveTime = now.with(sendTime);
                    // 如果当前时间已经过了发送时间，则从明天开始
                    if (now.isAfter(effectiveTime)) {
                        effectiveTime = effectiveTime.plusDays(1);
                    }
                } else if ("WEEKLY".equals(request.bulletinType())) {
                    // 周报：本周或下周指定执行日的指定时间
                    DayOfWeek targetDay = DayOfWeek.of(Integer.parseInt(request.executionDay()));
                    effectiveTime = now.with(TemporalAdjusters.nextOrSame(targetDay)).with(sendTime);
                    // 如果当前日期就是执行日但已经过了发送时间，则设为下周
                    if (now.getDayOfWeek() == targetDay && now.isAfter(effectiveTime)) {
                        effectiveTime = effectiveTime.plusWeeks(1);
                    }
                } else if ("MONTHLY".equals(request.bulletinType())) {
                    // 月报：本月或下月指定日期的指定时间
                    int dayOfMonth = Integer.parseInt(request.executionDay());
                    // 处理当月或下月
                    LocalDateTime thisMonth = now.withDayOfMonth(
                        Math.min(dayOfMonth, now.toLocalDate().lengthOfMonth())
                    ).with(sendTime);
                    
                    if (now.isAfter(thisMonth)) {
                        // 如果已经过了本月执行日期，则设为下月
                        effectiveTime = now.plusMonths(1)
                            .withDayOfMonth(Math.min(dayOfMonth, now.plusMonths(1).toLocalDate().lengthOfMonth()))
                            .with(sendTime);
                    } else {
                        effectiveTime = thisMonth;
                    }
                } else {
                    // 默认今天或明天指定时间
                    effectiveTime = now.with(sendTime);
                    if (now.isAfter(effectiveTime)) {
                        effectiveTime = effectiveTime.plusDays(1);
                    }
                }
            }
            
            // 设置首次生效时间
            jobEntity.setBeginTime(effectiveTime);
        } catch (Exception e) {
            throw new RuntimeException("设置任务生效时间失败", e);
        }
    }

    /**
     * 根据简报类型生成cron表达式
     * @param request 简报任务请求
     * @param isGenerator 是否为生成任务
     * @return cron表达式
     */
    private String generateCronExpression(BulletinJobRequest request, boolean isGenerator) {
        if (isGenerator) {
            // 生成任务固定在凌晨2点执行
            if ("DAILY".equals(request.bulletinType())) {
                // 每天凌晨2点执行
                return "0 0 2 * * ?";
            } else if ("WEEKLY".equals(request.bulletinType())) {
                // 每周指定日期凌晨2点执行
                String dayOfWeek = mapWeekdayToCron(request.executionDay());
                return String.format("0 0 2 ? * %s", dayOfWeek);
            } else if ("MONTHLY".equals(request.bulletinType())) {
                // 每月指定日期凌晨2点执行
                String dayOfMonth = request.executionDay();
                return String.format("0 0 2 %s * ?", dayOfMonth);
            }
            
            // 默认每天凌晨2点执行
            return "0 0 2 * * ?";
        } else {
            // 发送任务使用用户配置的时间
            String[] timeParts = request.sendTime().split(":");
            String hour = timeParts[0];
            String minute = timeParts[1];
            
            if ("DAILY".equals(request.bulletinType())) {
                // 每天指定时间执行
                return String.format("0 %s %s * * ?", minute, hour);
            } else if ("WEEKLY".equals(request.bulletinType())) {
                // 每周指定日期和时间执行
                String dayOfWeek = mapWeekdayToCron(request.executionDay());
                return String.format("0 %s %s ? * %s", minute, hour, dayOfWeek);
            } else if ("MONTHLY".equals(request.bulletinType())) {
                // 每月指定日期和时间执行
                String dayOfMonth = request.executionDay();
                return String.format("0 %s %s %s * ?", minute, hour, dayOfMonth);
            }
            
            // 默认每天执行
            return String.format("0 %s %s * * ?", minute, hour);
        }
    }
    
    /**
     * 将任务实体转换为请求DTO
     */
    private BulletinJobRequest convertToRequest(JobEntity job) {
        // 解析任务参数
        String jobParams = job.getJobParams();
        BulletinParams params = JsonUtil.fromJson(jobParams, BulletinParams.class);
        
        // 从任务名称中提取基本名称（去掉"-发送"后缀）
        String jobName = job.getName();
        String baseName = jobName.replace("-发送", "");
        
        // 从cron表达式中提取发送时间
        String cron = job.getCron();
        String sendTime = extractSendTimeFromCron(cron);
        
        // 构建请求DTO
        return new BulletinJobRequest(
                job.getId(),
                job.getPlanId(),
                baseName,
                job.getCycleType(),
                sendTime,
                job.getExecutionDay(),
                job.getEnabled(),
                params.bulletinTitle(),
                params.emailReceivers(),
                params.smsReceivers(),
                params.params(),
                params.groupIds()
        );
    }

    /**
     * 从cron表达式中提取发送时间
     */
    private String extractSendTimeFromCron(String cron) {
        // cron格式：秒 分 时 日 月 星期
        String[] parts = cron.split(" ");
        if (parts.length >= 3) {
            return parts[2] + ":" + parts[1]; // 时:分
        }
        return "00:00"; // 默认值
    }
} 