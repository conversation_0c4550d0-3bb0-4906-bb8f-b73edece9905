package com.czb.hn.service.bulletin;

import com.czb.hn.dto.response.briefing.BriefingSummaryDto;
import com.czb.hn.jpa.securadar.entity.BulletinContentDataEntity;

/**
 * 简报内容数据服务接口
 */
public interface BulletinContentDataService {
    
    /**
     * 根据任务参数生成简报内容数据并保存
     *
     * @param generationId 生成记录ID
     * @param planId 方案ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param params 任务参数
     * @return 生成的简报内容数据实体
     */
    BulletinContentDataEntity generateAndSaveBulletinContentData(
            Long generationId,
            Long planId,
            String startTime, 
            String endTime, 
            String params);
    
    /**
     * 根据生成记录ID获取简报内容数据
     *
     * @param generationId 生成记录ID
     * @return 简报内容数据DTO
     */
    BriefingSummaryDto getBulletinContentData(Long generationId);
}