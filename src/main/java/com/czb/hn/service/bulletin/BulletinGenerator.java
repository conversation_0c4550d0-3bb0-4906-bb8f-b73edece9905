package com.czb.hn.service.bulletin;

import java.time.LocalDate;
import java.util.List;

/**
 * 简报生成器接口
 * 负责根据简报类型和数据范围生成简报内容
 */
public interface BulletinGenerator {
    
    /**
     * 生成简报内容
     *
     * @param bulletinType 简报类型（DAILY/WEEKLY/MONTHLY）
     * @param bulletinDate 简报日期
     * @param dateRange 数据日期范围（起止日期）
     * @param groupIds 部门/组织IDs
     * @return 生成的简报内容（二进制格式）
     */
    byte[] generateBulletin(String bulletinType, LocalDate bulletinDate, DateRange dateRange, Long generationId);
    
    /**
     * 日期范围类
     */
    record DateRange(LocalDate startDate, LocalDate endDate) {
    }
} 