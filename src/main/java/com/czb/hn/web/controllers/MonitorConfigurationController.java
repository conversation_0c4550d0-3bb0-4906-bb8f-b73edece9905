package com.czb.hn.web.controllers;

import com.czb.hn.dto.monitor.MonitorConfigurationResponseDto;
import com.czb.hn.dto.monitor.MonitorConfigurationUpdateDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.MonitorConfigurationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/monitor-configuration")
@Tag(name = "MonitorConfigurationController", description = "监测配置管理")
public class MonitorConfigurationController {

    private static final Logger logger = LoggerFactory.getLogger(MonitorConfigurationController.class);

    @Autowired
    private MonitorConfigurationService monitorConfigurationService;

    @GetMapping("/{planId}")
    @Operation(summary = "根据方案id获取监测配置")
    public ResponseEntity<ApiResponse<MonitorConfigurationResponseDto>> getConfiguration(
            @Parameter(description = "Plan ID") @PathVariable Long planId) {
        try {
            logger.info("Getting monitor configuration with Plan ID: {}", planId);
            MonitorConfigurationResponseDto response = monitorConfigurationService.getConfigurationsByPlanId(planId);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "Configuration retrieved successfully", response));
        } catch (IllegalArgumentException e) {
            logger.warn("Configuration not found with ID: {}", planId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        } catch (Exception e) {
            logger.error("Error getting alert configuration with ID {}: {}", planId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR","Failed to get monitor configuration: "+e.getMessage(), null));
        }
    }

    @PutMapping("/{planId}")
    @Operation(summary = "更新监测配置")
    public ResponseEntity<ApiResponse<MonitorConfigurationResponseDto>> updateConfiguration(
            @Parameter(description = "Plan ID") @PathVariable Long planId,
            @RequestBody MonitorConfigurationUpdateDto updateDto) {
        try {
            logger.info("Updating monitor configuration with ID: {}", planId);
            MonitorConfigurationResponseDto response = monitorConfigurationService.updateConfiguration(planId, updateDto);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "Configuration updated successfully", response));
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid input for updating monitor configuration {}: {}", planId, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "Invalid input: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("Error updating monitor configuration with ID {}: {}", planId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "Failed to update monitor configuration: " + e.getMessage(), null));
        }
    }
}
