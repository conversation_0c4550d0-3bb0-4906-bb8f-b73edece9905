package com.czb.hn.web.controllers;

import cn.com.ycfin.onepass.client.OnePassClient;
import cn.com.ycfin.onepass.client.OnePassRedirect;
import cn.com.ycfin.onepass.client.response.GetUserRolesResponse;
import cn.com.ycfin.onepass.client.security.JsonUtil;
import com.czb.hn.config.UserContextInterceptor;
import com.czb.hn.dto.auth.OnePassToken;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.user.GroupInfo;
import com.czb.hn.dto.user.LoginUser;
import com.czb.hn.dto.user.LoginUserContext;
import com.czb.hn.dto.user.LoginUserContextHolder;
import com.czb.hn.service.business.GroupCacheService;
import com.czb.hn.service.user.UserLoginRecordService;
import com.czb.hn.config.ratelimit.RateLimit;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.shaded.json.parser.ParseException;
import com.nimbusds.oauth2.sdk.id.State;
import com.nimbusds.openid.connect.sdk.OIDCTokenResponse;
import com.nimbusds.openid.connect.sdk.token.OIDCTokens;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/onepass")
@Tag(name = "LoginController", description = "提供用户认证相关的API")
public class LoginController {

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    private static final String LOGIN_STATE = "login_state";
    public static final String DEV_REDIRECT_URL = "http://localhost:3000/login/code";
    private static final String SPRING_SECURITY_CONTEXT = HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY;

    @Autowired
    private OnePassClient onePassClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private GroupCacheService groupCacheService;

    @Autowired
    private UserLoginRecordService userLoginRecordService;

    @GetMapping("/login")
    @Operation(summary = "登录", description = "重定向到OnePass登录页面")
    public void login(@RequestParam(required = false) String successUri, HttpServletRequest request,
            HttpServletResponse response) throws IOException {
        State state = new State(16);
        request.getSession().setAttribute(LOGIN_STATE, state.getValue());
        String redirectUrl = onePassClient.getRedirectUri().toString();
        if (isDev(request)) {
            redirectUrl = DEV_REDIRECT_URL;
        }

        if (StringUtils.isNotBlank(successUri)) {
            redirectUrl = redirectUrl + "?success_uri=" + successUri;
        }


        String authUrl = onePassClient.buildAuthorizeUrl(state, redirectUrl);
        response.setContentType("application/json");
        PrintWriter printWriter = response.getWriter();
        printWriter.write(objectMapper.writeValueAsString(new OnePassRedirect(authUrl)));
        printWriter.flush();
        printWriter.close();
    }

    @GetMapping("/login/code")
    @Operation(summary = "回调", description = "处理OnePass认证回调")
    public ResponseEntity<ApiResponse<Map<String, Object>>> codeLogin(
            @RequestParam("code") String code,
            @RequestParam("state") String state,
            HttpServletRequest request, HttpServletResponse response)
            throws ParseException {
        try {
            // 获取授权码
            Map<String, Object> map = new HashMap<>();
            request.getSession().removeAttribute(LOGIN_STATE);
            OIDCTokenResponse oidcTokenResponse;
            if (isDev(request)) {
                oidcTokenResponse = onePassClient.getOIDCToken(code, DEV_REDIRECT_URL);
            } else {
                oidcTokenResponse = onePassClient.getOIDCToken(code);
            }
            if (oidcTokenResponse == null || oidcTokenResponse.getOIDCTokens() == null) {
                throw new RuntimeException("登录授权失败.");
            }

            if (code == null || code.isEmpty()) {
                throw new RuntimeException("Authorization code is missing");
            }

            OIDCTokens oidcTokens = oidcTokenResponse.getOIDCTokens();

            String jsonStr = null;
            jsonStr = JsonUtil.toJsonString(oidcTokens.getIDToken().getJWTClaimsSet().getClaims());
            JsonNode jsonNode = JsonUtil.toJsonNode(jsonStr);
            if (jsonNode == null) {
                return null;
            }
            JsonNode userInfoJsonNode = jsonNode.get("userinfo");

            if (userInfoJsonNode == null) {
                throw new RuntimeException("User info is missing");
            }
            String userId = userInfoJsonNode.get("sub").asText();
            String name = userInfoJsonNode.get("name").asText();
            GetUserRolesResponse getUserRolesResponse = onePassClient.getUserRoles(userId);
            JsonNode rolesJsonNode = userInfoJsonNode.get("roles");
            HashSet<GrantedAuthority> grantedAuthorities = new HashSet<>();

            if (rolesJsonNode != null) {
                for (JsonNode role : rolesJsonNode) {
                    String roleName = role.get("roleName").asText();
                    grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_" + roleName));
                }
            }

            // 转换为 OnePassAuthenticationToken
            OnePassToken onePassAuthenticationToken;
            onePassAuthenticationToken = new OnePassToken(oidcTokens, userInfoJsonNode, userId, name,
                    getUserRolesResponse.getUserRoles(), null, grantedAuthorities);

            // 创建LoginUser对象并设置到LoginUserContextHolder
            LoginUser loginUser = new LoginUser();
            loginUser.setUserId(userId);
            loginUser.setName(name);
            loginUser.setUserRoles(getUserRolesResponse.getUserRoles());

            // 从userInfoJsonNode中提取更多信息并设置
            if (userInfoJsonNode.has("mobile")) {
                loginUser.setPhoneNumber(userInfoJsonNode.get("mobile").asText());
            }
            if (userInfoJsonNode.has("employeeId")) {
                loginUser.setEmployeeId(userInfoJsonNode.get("employeeId").asText());
            }

            if (userInfoJsonNode != null && userInfoJsonNode.has("groups")) {
                Set<String> groupNames = new HashSet<>();
                String primaryGroupName = null;
                String primaryGroupId = null;
                String defaultGroupName = null;
                String defaultGroupId = null;
                JsonNode groupsNode = userInfoJsonNode.get("groups");
                if (groupsNode != null && groupsNode.isArray()) {
                    for (JsonNode groupNode : groupsNode) {
                        if (groupNode != null) {
                            if (groupNode.has("isPrimary") && groupNode.get("isPrimary").asBoolean()) {
                                if (groupNode.has("groupName")) {
                                    primaryGroupName = groupNode.get("groupName").asText();
                                }
                                if (groupNode.has("groupId")) {
                                    primaryGroupId = groupNode.get("groupId").asText();
                                }
                            }
                            if (groupNode.has("groupName")) {
                                defaultGroupName = groupNode.get("groupName").asText();
                            }
                            if (groupNode.has("groupId")) {
                                defaultGroupId = groupNode.get("groupId").asText();
                            }
                            if (groupNode.has("groupName")) {
                                groupNames.add(groupNode.get("groupName").asText());
                            }
                        }
                    }
                }

                // 设置组相关信息
                loginUser.setGroupNames(groupNames);

                // 设置主要组名称
                loginUser.setPrimaryGroupName(primaryGroupName != null ? primaryGroupName : defaultGroupName);

                // 设置主要组ID
                String finalGroupId = primaryGroupId != null ? primaryGroupId : defaultGroupId;
                loginUser.setPrimaryGroupId(finalGroupId);

                // 从缓存中获取组代码并设置
                if (finalGroupId != null) {
                    enrichGroupCodeFromCache(loginUser, finalGroupId);
                } else {
                    logger.warn("无法获取用户 {} 的组织ID", userId);
                }

            }

            // 保存用户上下文
            LoginUserContext userContext = new LoginUserContext();
            userContext.setUser(loginUser);
            LoginUserContextHolder.setContext(userContext);

            // 设置SecurityContext
            SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
            securityContext.setAuthentication(onePassAuthenticationToken);
            SecurityContextHolder.setContext(securityContext);

            // 重要：将SecurityContext保存到会话中
            HttpSession session = request.getSession(true);
            session.setAttribute(SPRING_SECURITY_CONTEXT, securityContext);

            String userCenterUrl = onePassClient.getUserCenterUrl();
            loginUser.setUserinfo(userInfoJsonNode);
            loginUser.setUserCenter(userCenterUrl);
            // 使用UserContextInterceptor保存LoginUser到会话
            UserContextInterceptor.saveUserToSession(request, loginUser);

            // 记录用户登录信息
            try {
                userLoginRecordService.recordLogin(
                        loginUser.getUserId(),
                        loginUser.getName(),
                        loginUser.getPrimaryGroupId(),
                        loginUser.getPrimaryGroupCode());
            } catch (Exception e) {
                logger.warn("Failed to record login for user: {}", userId, e);
                // 不影响登录流程，只记录警告日志
            }

            logger.info("User authenticated successfully: {}", userId);

            map.put("userinfo", userInfoJsonNode);
            map.put("user-center", userCenterUrl);
            return ResponseEntity.ok(ApiResponse.success(map));
        } catch (Exception e) {
            logger.error("Failed to process authentication callback", e);
            return null;
        }
    }

    /**
     * 从组织缓存中获取组代码并设置到用户信息中
     * 如果当前组织没有代码，将尝试获取其父组织的信息
     * 
     * @param loginUser 登录用户信息
     * @param groupId   组织ID
     */
    private void enrichGroupCodeFromCache(LoginUser loginUser, String groupId) {
        try {
            if (groupId == null) {
                logger.warn("组织ID为空，无法获取组织代码");
                return;
            }

            // 从缓存获取组织信息
            Optional<GroupInfo> groupInfo = groupCacheService.getGroupById(groupId);

            if (groupInfo.isPresent()) {
                GroupInfo group = groupInfo.get();

                // 如果有代码，直接使用当前组织的完整信息
                if (StringUtils.isNotBlank(group.groupCode())) {
                    // 同步更新所有组织相关信息
                    loginUser.setPrimaryGroupCode(group.groupCode());
                    loginUser.setPrimaryGroupName(group.groupName());
                    loginUser.setPrimaryGroupId(group.groupId());

                    logger.debug("从缓存中获取完整组织信息: ID={}, 名称={}, 代码={}",
                            group.groupId(), group.groupName(), group.groupCode());
                    return; // 已找到有效信息，直接返回
                }

                // 如果当前组织没有代码，尝试递归查找父组织
                String parentId = group.parentId();
                if (StringUtils.isNotBlank(parentId)) {
                    logger.debug("当前组织 {} 没有代码，尝试从父组织 {} 获取", groupId, parentId);
                    findGroupCodeFromParent(loginUser, parentId, 0);
                }
            } else {
                logger.debug("组织 {} 在缓存中不存在，尝试刷新缓存", groupId);

                // 尝试刷新缓存并再次获取
                groupCacheService.refreshCache();
                groupInfo = groupCacheService.getGroupById(groupId);

                if (groupInfo.isPresent()) {
                    GroupInfo group = groupInfo.get();

                    // 如果有代码，直接使用当前组织的完整信息
                    if (StringUtils.isNotBlank(group.groupCode())) {
                        // 同步更新所有组织相关信息
                        loginUser.setPrimaryGroupCode(group.groupCode());
                        loginUser.setPrimaryGroupName(group.groupName());
                        loginUser.setPrimaryGroupId(group.groupId());

                        logger.debug("刷新缓存后获取完整组织信息: ID={}, 名称={}, 代码={}",
                                group.groupId(), group.groupName(), group.groupCode());
                        return; // 已找到有效信息，直接返回
                    }

                    // 如果当前组织没有代码，尝试递归查找父组织
                    String parentId = group.parentId();
                    if (StringUtils.isNotBlank(parentId)) {
                        logger.debug("当前组织 {} 没有代码，尝试从父组织 {} 获取", groupId, parentId);
                        findGroupCodeFromParent(loginUser, parentId, 0);
                    }
                } else {
                    logger.warn("无法获取组织 {} 的信息", groupId);
                }
            }
        } catch (Exception e) {
            logger.error("从缓存获取组织代码异常", e);
        }
    }

    /**
     * 递归从父组织获取组织代码
     * 
     * @param loginUser 登录用户信息
     * @param parentId  父组织ID
     * @param depth     递归深度（防止无限递归）
     * @return 是否成功找到并设置了组织代码
     */
    private boolean findGroupCodeFromParent(LoginUser loginUser, String parentId, int depth) {
        // 防止无限递归，最多查找5层父组织
        if (depth >= 5 || StringUtils.isBlank(parentId)) {
            return false;
        }

        Optional<GroupInfo> parentGroup = groupCacheService.getGroupById(parentId);
        if (parentGroup.isPresent()) {
            GroupInfo parent = parentGroup.get();

            // 如果父组织有代码，使用父组织的完整信息
            if (StringUtils.isNotBlank(parent.groupCode())) {
                // 更新用户的所有组织相关信息为父组织的信息
                loginUser.setPrimaryGroupCode(parent.groupCode());
                loginUser.setPrimaryGroupName(parent.groupName());
                loginUser.setPrimaryGroupId(parent.groupId());

                logger.debug("从父组织获取完整信息: ID={}, 名称={}, 代码={}",
                        parent.groupId(), parent.groupName(), parent.groupCode());
                return true;
            }

            // 否则继续递归查找更上层的父组织
            String grandParentId = parent.parentId();
            if (StringUtils.isNotBlank(grandParentId)) {
                return findGroupCodeFromParent(loginUser, grandParentId, depth + 1);
            }
        }

        return false;
    }

    @PostMapping("/logout")
    @Operation(summary = "登出", description = "用户登出系统")
    public ResponseEntity<ApiResponse<Void>> logout(HttpServletRequest request) {
        try {
            // 清除认证上下文
            SecurityContextHolder.clearContext();
            LoginUserContextHolder.clearContext();

            // 使会话无效
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.invalidate();
            }

            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            logger.error("Logout failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("登出失败: " + e.getMessage()));
        }
    }

    @GetMapping("/current-user")
    @Operation(summary = "获取当前用户", description = "获取当前登录用户信息")
    public ResponseEntity<ApiResponse<LoginUser>> getCurrentUser(HttpServletRequest request) {
        try {
            // 首先尝试从会话中获取用户信息
            HttpSession session = request.getSession(false);
            LoginUser currentUser = null;

            if (session != null) {
                currentUser = (LoginUser) session.getAttribute("CURRENT_USER");

                // 如果会话中有用户，恢复到ThreadLocal
                if (currentUser != null) {
                    LoginUserContext userContext = new LoginUserContext();
                    userContext.setUser(currentUser);
                    LoginUserContextHolder.setContext(userContext);
                    logger.debug("User restored from session: {}", currentUser.getUserId());
                }
            }

            // 如果会话中没有用户信息，则尝试从SecurityContext获取
            if (currentUser == null) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

                if (authentication == null || !authentication.isAuthenticated()) {
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(ApiResponse.error("用户未登录"));
                }

                // 尝试从LoginUserContextHolder获取用户
                currentUser = LoginUserContextHolder.getContext() != null
                        ? LoginUserContextHolder.getContext().getUser()
                        : null;
            }

            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("无法获取用户信息"));
            }

            return ResponseEntity.ok(ApiResponse.success(currentUser));
        } catch (Exception e) {
            logger.error("Failed to get current user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取用户信息失败: " + e.getMessage()));
        }
    }

    @GetMapping("/refresh-groups-cache")
    @Operation(summary = "刷新组织缓存", description = "手动刷新组织信息缓存")
    @RateLimit(permits = 0.1, ipPermits = 0.05, timeout = 500, description = "刷新组织缓存操作限流: 每10秒最多允许1次请求，每IP每20秒最多1次")
    public ResponseEntity<ApiResponse<Void>> refreshGroupsCache() {
        try {
            groupCacheService.refreshCache();
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            logger.error("刷新组织缓存失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("刷新组织缓存失败: " + e.getMessage()));
        }
    }

    private boolean isDev(HttpServletRequest request) {
        String env = request.getHeader("env");
        return env != null && env.equals("dev");
    }
}
