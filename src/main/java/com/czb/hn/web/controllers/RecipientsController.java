package com.czb.hn.web.controllers;

import com.czb.hn.dto.recipients.RecipientsCreateDto;
import com.czb.hn.dto.recipients.RecipientsRequireDto;
import com.czb.hn.dto.recipients.RecipientsResponseDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.RecipientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@RestController
@RequestMapping("/recipients")
@Tag(name = "RecipientsController", description = "收件人列表管理")
public class RecipientsController {

    private static final Logger logger = LoggerFactory.getLogger(RecipientsController.class);

    @Autowired
    private RecipientService recipientService;

    @GetMapping("/search")
    @Operation(summary = "根据方案获取收件人列表")
    public ResponseEntity<ApiResponse<List<RecipientsResponseDto>>> getRecipientsList(
            @Schema(description = "方案ID")
            Long planId,
            @Schema(description = "系统类型")
            Integer systemType,
            @Schema(description = "接收类型  EMAIL SMS")
            String receiveType) {
        try {
            logger.info("根据方案获取收件人列表");
            List<RecipientsResponseDto> recipientsList = recipientService.getRecipientsList(new RecipientsRequireDto(planId, systemType, receiveType));
            return ResponseEntity.ok(ApiResponse.success(recipientsList));
        } catch (Exception e) {
            logger.error("Error retrieving recipients list: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve recipients list: " + e.getMessage()));
        }
    }

    @PostMapping("/add")
    @Operation(summary = "添加接收人")
    public ResponseEntity<ApiResponse<Boolean>> createRecipientsList(
            @RequestBody RecipientsCreateDto createDto) {
        try {
            logger.info("添加接收人信息: {}", createDto);
            return ResponseEntity.ok(ApiResponse.success(recipientService.createRecipients(createDto)));
        } catch (Exception e) {
            logger.error("添加接收人信息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("",false));
        }
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除接收人信息")
    public ResponseEntity<ApiResponse<Boolean>> deleteRecipientsList(
            @PathVariable("id") Long id) {
        try {
            return ResponseEntity.ok(ApiResponse.success(recipientService.deleteRecipients(id)));
        } catch (Exception e) {
            logger.error("删除接收人信息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("", false));
        }
    }


    @PostMapping("/enable/{id}")
    @Operation(summary = "启用接收人")
    public ResponseEntity<ApiResponse<Boolean>> enableRecipient(
            @PathVariable("id") Long id) {
        try {
            return ResponseEntity.ok(ApiResponse.success(recipientService.enable(id, true)));
        } catch (Exception e) {
            logger.error("添加接收人信息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("",false));
        }
    }

    @PostMapping("/disable/{id}")
    @Operation(summary = "禁用接收人")
    public ResponseEntity<ApiResponse<Boolean>> disableRecipient(
            @PathVariable("id") Long id) {
        try {
            return ResponseEntity.ok(ApiResponse.success(recipientService.enable(id, false)));
        } catch (Exception e) {
            logger.error("添加接收人信息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("",false));
        }
    }
}
