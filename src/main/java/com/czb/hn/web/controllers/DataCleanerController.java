package com.czb.hn.web.controllers;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.enums.SourceType;
import com.czb.hn.jpa.securadar.entity.SinaNewsDwdEntity;
import com.czb.hn.jpa.securadar.entity.SinaNewsOdsEntity;
import com.czb.hn.jpa.securadar.repository.SinaNewsOdsRepository;
import com.czb.hn.service.cleaner.SinaNewsCleanerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 数据清洗控制器
 * 提供数据清洗相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/test/data-cleaner")
@Tag(name = "DataCleanerController", description = "数据清洗管理接口，负责ODS到DWD的数据转换和清洗")
public class DataCleanerController {

    @Autowired
    private SinaNewsCleanerService cleanerService;

    @Autowired
    private SinaNewsOdsRepository odsRepository;

    /**
     * 手动触发数据清洗
     */
    @PostMapping("/process/all")
    @Operation(summary = "处理所有未清洗数据", description = "手动触发处理所有未清洗的ODS数据到DWD层")
    public ResponseEntity<ApiResponse<Map<String, Object>>> processAllUnprocessedRecords() {
        try {
            log.info("手动触发数据清洗：处理所有未清洗数据");
            
            long startTime = System.currentTimeMillis();
            int processedCount = cleanerService.processAllUnprocessedRecords();
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> result = new HashMap<>();
            result.put("processedCount", processedCount);
            result.put("processingTimeMs", endTime - startTime);
            result.put("timestamp", System.currentTimeMillis());
            result.put("triggerType", "manual");
            
            String message = String.format("数据清洗完成，共处理 %d 条记录，耗时 %d 毫秒", 
                    processedCount, endTime - startTime);
            
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("数据清洗失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("数据清洗失败: " + e.getMessage()));
        }
    }

    /**
     * 处理单条记录
     */
    @PostMapping("/process/single/{contentId}")
    @Operation(summary = "处理单条记录", description = "根据内容ID处理单条ODS记录到DWD层")
    public ResponseEntity<ApiResponse<SinaNewsDwdEntity>> processSingleRecord(
            @Parameter(description = "内容ID", required = true) @PathVariable String contentId) {
        try {
            log.info("手动触发单条记录清洗：{}", contentId);
            
            Optional<SinaNewsOdsEntity> odsRecord = odsRepository.findByContentId(contentId);
            if (odsRecord.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("未找到指定的ODS记录: " + contentId));
            }
            
            SinaNewsDwdEntity result = cleanerService.processRecord(odsRecord.get());
            
            log.info("单条记录清洗完成：{}", contentId);
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("单条记录清洗失败：{}", contentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("记录处理失败: " + e.getMessage()));
        }
    }


    /**
     * 根据内容ID查询已清洗的记录
     */
    @GetMapping("/records/{contentId}")
    @Operation(summary = "根据内容ID查询已清洗记录", description = "根据内容ID查询已清洗的DWD记录")
    public ResponseEntity<ApiResponse<SinaNewsDwdEntity>> getRecordByContentId(
            @Parameter(description = "内容ID", required = true) @PathVariable String contentId) {
        try {
            SinaNewsDwdEntity record = cleanerService.findByContentId(contentId);
            
            if (record == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("未找到指定的DWD记录: " + contentId));
            }
            
            return ResponseEntity.ok(ApiResponse.success(record));
            
        } catch (Exception e) {
            log.error("根据内容ID查询记录失败：{}", contentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("查询失败: " + e.getMessage()));
        }
    }


    /**
     * 检查记录是否已处理
     */
    @GetMapping("/check/{contentId}")
    @Operation(summary = "检查记录处理状态", description = "检查指定内容ID的记录是否已经处理")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkRecordStatus(
            @Parameter(description = "内容ID", required = true) @PathVariable String contentId) {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 检查ODS记录
            Optional<SinaNewsOdsEntity> odsRecord = odsRepository.findByContentId(contentId);
            boolean odsExists = odsRecord.isPresent();
            boolean odsProcessed = odsExists && odsRecord.get().getProcessed();
            
            // 检查DWD记录
            SinaNewsDwdEntity dwdRecord = cleanerService.findByContentId(contentId);
            boolean dwdExists = dwdRecord != null;
            
            status.put("contentId", contentId);
            status.put("odsExists", odsExists);
            status.put("odsProcessed", odsProcessed);
            status.put("dwdExists", dwdExists);
            status.put("fullyProcessed", odsProcessed && dwdExists);
            status.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(ApiResponse.success(status));
            
        } catch (Exception e) {
            log.error("检查记录状态失败：{}", contentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("状态检查失败: " + e.getMessage()));
        }
    }
}
