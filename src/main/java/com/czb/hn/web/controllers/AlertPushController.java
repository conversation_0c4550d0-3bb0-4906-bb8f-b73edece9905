package com.czb.hn.web.controllers;

import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.AlertPushService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Alert Push Controller
 * 预警推送管理控制器，提供预警推送记录的查询、管理和统计API
 */
@RestController
@RequestMapping("/alert-push")
@Tag(name = "AlertPushController", description = "预警推送记录查询、管理和统计API")
@Slf4j
public class AlertPushController {

    @Autowired
    private AlertPushService alertPushService;


    /**
     * 根据预警ID查询推送详情
     */
    @GetMapping("/details/alert/{alertId}")
    @Operation(summary = "根据预警ID查询推送详情", description = "获取指定预警的所有推送详情记录")
    public ResponseEntity<ApiResponse<List<AlertPushDetailResponseDto>>> getPushDetailsByAlertId(
            @Parameter(description = "预警ID") @PathVariable Long alertId) {

        try {
            log.info("Getting push details for alert ID: {}", alertId);
            List<AlertPushDetailResponseDto> pushDetails = alertPushService.getPushDetailsByAlertId(alertId);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "查询成功", pushDetails));
        } catch (Exception e) {
            log.error("Failed to get push details for alert ID: {}", alertId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "查询推送详情失败: " + e.getMessage(), null));
        }
    }


}
