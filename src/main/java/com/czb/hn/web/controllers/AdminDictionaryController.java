package com.czb.hn.web.controllers;

import com.czb.hn.dto.DictionaryItemDTO;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.DictionaryService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/admin/dictionary")
public class AdminDictionaryController {

    private final DictionaryService dictionaryService;

    public AdminDictionaryController(DictionaryService dictionaryService) {
        this.dictionaryService = dictionaryService;
    }

    @GetMapping("")
    @Operation(summary = "获取所有字典项", description = "itemTypes - 字典类型，多个类型用逗号分隔")
    public ResponseEntity<ApiResponse<Map<String, List<DictionaryItemDTO>>>> getAllDictionaryItems(@RequestParam(value = "itemTypes", required = false) String itemTypes) {
        if (StringUtils.hasLength(itemTypes)) {
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功",dictionaryService.getItemsByTypes(List.of(itemTypes.split(",")))));
        }
        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功",dictionaryService.getAllItems()));
    }

    @GetMapping("/itemTypes")
    @Operation(summary = "获取所有字典类型")
    public ResponseEntity<ApiResponse<List<String>>> getAllItemTypes() {
        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", dictionaryService.getAllItemTypes()));
    }

    @GetMapping("/{itemType}")
    @Operation(summary = "获取指定字典类型下的所有字典项")
    public ResponseEntity<ApiResponse<List<DictionaryItemDTO>>> getDictionaryItemsByType(@PathVariable String itemType) {
        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", dictionaryService.getItemsByType(itemType)));
    }

    @PostMapping()
    @Operation(summary = "添加字典项")
    public ResponseEntity<ApiResponse<DictionaryItemDTO>> addDictionaryItem(@RequestBody @Valid DictionaryItemDTO item) {
        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", dictionaryService.addItem(item)));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新字典项")
    public ResponseEntity<ApiResponse<DictionaryItemDTO>> updateDictionaryItem(@PathVariable Long id, @RequestBody @Valid DictionaryItemDTO item) {
        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", dictionaryService.updateItem(id, item)));
    }

}
