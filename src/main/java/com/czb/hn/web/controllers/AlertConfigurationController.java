package com.czb.hn.web.controllers;

import com.czb.hn.dto.alert.*;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.AlertConfigurationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for Alert Configuration Management
 * Provides endpoints for CRUD operations and snapshot management
 */
@RestController
@RequestMapping("/alert-configurations")
@Tag(name = "AlertConfigurationController", description = "舆情监控预警配置相关接口")
public class AlertConfigurationController {

        private static final Logger logger = LoggerFactory.getLogger(AlertConfigurationController.class);

        @Autowired
        private AlertConfigurationService alertConfigurationService;

        @PostMapping
        @Operation(summary = "创建预警配置", description = "创建新的舆情监控预警配置")
        public ResponseEntity<ApiResponse<AlertConfigurationResponseDto>> createConfiguration(
                        @RequestBody AlertConfigurationCreateDto createDto) {
                try {
                        logger.info("Creating alert configuration: {}", createDto.name());
                        AlertConfigurationResponseDto response = alertConfigurationService
                                        .createConfiguration(createDto);
                        return ResponseEntity.status(HttpStatus.CREATED)
                                        .body(new ApiResponse<>("SUCCESS", "Alert configuration created successfully",
                                                        response));
                } catch (IllegalArgumentException e) {
                        logger.warn("Invalid input for creating alert configuration: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "Invalid input: " + e.getMessage(), null));
                } catch (Exception e) {
                        logger.error("Error creating alert configuration: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR",
                                                        "Failed to create alert configuration: " + e.getMessage(),
                                                        null));
                }
        }

        @GetMapping("/{id}")
        @Operation(summary = "获取预警配置", description = "根据ID获取预警配置详情")
        public ResponseEntity<ApiResponse<AlertConfigurationResponseDto>> getConfiguration(
                        @Parameter(description = "Configuration ID") @PathVariable Long id) {
                try {
                        logger.info("Getting alert configuration with ID: {}", id);
                        AlertConfigurationResponseDto response = alertConfigurationService.getConfigurationById(id);
                        return ResponseEntity.ok(
                                        new ApiResponse<>("SUCCESS", "Configuration retrieved successfully", response));
                } catch (IllegalArgumentException e) {
                        logger.warn("Configuration not found with ID: {}", id);
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                        .body(new ApiResponse<>("ERROR", e.getMessage(), null));
                } catch (Exception e) {
                        logger.error("Error getting alert configuration with ID {}: {}", id, e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR",
                                                        "Failed to get alert configuration: " + e.getMessage(), null));
                }
        }

        @PutMapping("/{id}")
        @Operation(summary = "更新预警配置", description = "更新现有的预警配置")
        public ResponseEntity<ApiResponse<AlertConfigurationResponseDto>> updateConfiguration(
                        @Parameter(description = "配置ID") @PathVariable Long id,
                        @RequestBody AlertConfigurationUpdateDto updateDto) {
                try {
                        logger.info("Updating alert configuration with ID: {}", id);
                        AlertConfigurationResponseDto response = alertConfigurationService.updateConfiguration(id,
                                        updateDto);
                        return ResponseEntity.ok(
                                        new ApiResponse<>("SUCCESS", "Configuration updated successfully", response));
                } catch (IllegalArgumentException e) {
                        logger.warn("Invalid input for updating alert configuration {}: {}", id, e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "Invalid input: " + e.getMessage(), null));
                } catch (Exception e) {
                        logger.error("Error updating alert configuration with ID {}: {}", id, e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR",
                                                        "Failed to update alert configuration: " + e.getMessage(),
                                                        null));
                }
        }

        @DeleteMapping("/{id}")
        @Operation(summary = "删除预警配置", description = "软删除预警配置")
        public ResponseEntity<ApiResponse<Void>> deleteConfiguration(
                        @Parameter(description = "配置ID") @PathVariable Long id,
                        @Parameter(description = "执行删除操作的用户") @RequestParam(required = false) String deletedBy,
                        @Parameter(description = "删除原因") @RequestParam(required = false) String deleteReason) {
                try {
                        logger.info("Deleting alert configuration with ID: {}", id);
                        alertConfigurationService.deleteConfiguration(id, deletedBy, deleteReason);
                        return ResponseEntity
                                        .ok(new ApiResponse<>("SUCCESS", "Configuration deleted successfully", null));
                } catch (IllegalArgumentException e) {
                        logger.warn("Configuration not found for deletion with ID: {}", id);
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                        .body(new ApiResponse<>("ERROR", e.getMessage(), null));
                } catch (Exception e) {
                        logger.error("Error deleting alert configuration with ID {}: {}", id, e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR",
                                                        "Failed to delete alert configuration: " + e.getMessage(),
                                                        null));
                }
        }

        @GetMapping("/by-plan/{planId}")
        @Operation(summary = "按方案获取配置", description = "获取指定方案的预警配置")
        public ResponseEntity<ApiResponse<AlertConfigurationResponseDto>> getConfigurationsByPlan(
                        @Parameter(description = "方案ID") @PathVariable Long planId) {
                try {
                        logger.info("Getting alert configurations for plan: {}", planId);

                        List<AlertConfigurationResponseDto> alertConfigs = alertConfigurationService
                                .getEnabledConfigurationsByPlanId(planId);
                        if (alertConfigs.isEmpty()) {
                                return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "Configurations retrieved successfully",
                                        null));
                        }

                        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "Configurations retrieved successfully",
                                alertConfigs.get(0)));
                } catch (Exception e) {
                        logger.error("Error getting alert configurations for plan {}: {}", planId, e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR",
                                                        "Failed to get alert configurations: " + e.getMessage(), null));
                }
        }
}
