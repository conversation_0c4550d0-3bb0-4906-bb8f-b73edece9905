package com.czb.hn.web.share;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.response.briefing.BriefingSummaryDto;
import com.czb.hn.service.bulletin.BulletinContentDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 简报内容数据控制器
 */
@RestController
@RequestMapping("/share/bulletin/content")
@Tag(name = "ShareBulletinContentController", description = "分享链接-简报内容数据查询接口")
public class ShareBulletinContentController {

    private final BulletinContentDataService bulletinContentDataService;

    public ShareBulletinContentController(BulletinContentDataService bulletinContentDataService) {
        this.bulletinContentDataService = bulletinContentDataService;
    }

    /**
     * 获取简报内容数据
     *
     * @param generationId 生成记录ID
     * @return 简报内容数据
     */
    @GetMapping("/{generationId}")
    @Operation(summary = "获取简报内容数据", description = "根据生成记录ID获取简报的结构化内容数据")
    public ResponseEntity<ApiResponse<BriefingSummaryDto>> getBulletinContentData(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        try {
            BriefingSummaryDto data = bulletinContentDataService.getBulletinContentData(generationId);
            if (data == null) {
                return ResponseEntity.ok(ApiResponse.success(null));
            }
            return ResponseEntity.ok(ApiResponse.success(data));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取简报内容数据失败: " + e.getMessage()));
        }
    }
}