package com.czb.hn.enums.converter;

import com.czb.hn.enums.MediaLevel;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for mediaLevels
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "央级": NATIONAL (央级)
 * - "省级": PROVINCIAL (省级)
 * - "地市": MUNICIPAL (地市)
 * - "重点": KEY (重点)
 * - "中小": MEDIUM_SMALL (中小)
 * - "企业商业": COMMERCIAL (企业商业)
 */
@Converter(autoApply = true)
public class MediaLevelConverter implements AttributeConverter<MediaLevel, String> {

    @Override
    public String convertToDatabaseColumn(MediaLevel attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public MediaLevel convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return MediaLevel.fromChineseValue(dbData);
    }
}
