package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Information Sensitivity Type Enumeration
 * 敏感性分类枚举 - Integer存储
 *
 * API字段: contentExt.sensitivityTypes
 * 存储方式: Integer存储 (API值)
 * API值: 1=敏感, 2=非敏感, 3=中性
 */
public enum InformationSensitivityType implements StandardEnum<Integer> {
    SENSITIVE(1, "敏感"), // API值=1
    NON_SENSITIVE(2, "非敏感"), // API值=2
    NEUTRAL(3, "中性"); // API值=3

    private final Integer value;
    private final String description;

    InformationSensitivityType(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from legacy Boolean value to enum
     * 
     * @param isSensitive Legacy Boolean value (true = sensitive, false =
     *                    non-sensitive, null = neutral)
     * @return Corresponding enum value
     */
    public static InformationSensitivityType fromBoolean(Boolean isSensitive) {
        if (isSensitive == null) {
            return NEUTRAL;
        }
        return isSensitive ? SENSITIVE : NON_SENSITIVE;
    }

    /**
     * Convert from integer value (matching API format)
     *
     * @param sensitivityType Integer value (1 = sensitive, 2 = non-sensitive, 3 =
     *                        neutral)
     * @return Corresponding enum value
     */
    public static InformationSensitivityType fromInteger(Integer sensitivityType) {
        if (sensitivityType == null) {
            return NEUTRAL;
        }
        for (InformationSensitivityType type : values()) {
            if (type.getValue() == sensitivityType) {
                return type;
            }
        }
        return NEUTRAL; // 默认返回中性
    }

    /**
     * Convert from string value (for API compatibility)
     * 
     * @param value String representation of the integer value
     * @return Corresponding enum value
     */
    public static InformationSensitivityType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return NEUTRAL;
        }

        try {
            int intValue = Integer.parseInt(value.trim());
            return fromInteger(intValue);
        } catch (NumberFormatException e) {
            return NEUTRAL;
        }
    }

    /**
     * Convert to Boolean for backward compatibility
     *
     * @return Boolean representation (null for NEUTRAL, true for SENSITIVE,
     *         false for NON_SENSITIVE)
     */
    public Boolean toBoolean() {
        return switch (this) {
            case SENSITIVE -> true;
            case NON_SENSITIVE -> false;
            case NEUTRAL -> null;
        };
    }

    /**
     * Get integer value for API compatibility
     *
     * @return Integer value (1 = sensitive, 2 = non-sensitive, 3 = neutral)
     */
    public Integer toInteger() {
        return this.value;
    }

    /**
     * Convert from value (for database storage compatibility)
     *
     * @param value Integer value (1 = sensitive, 2 = non-sensitive, 3 = neutral)
     * @return Corresponding enum value
     */
    public static InformationSensitivityType fromValue(int value) {
        for (InformationSensitivityType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return NEUTRAL; // 默认返回中性
    }

    /**
     * Check if this is a sensitive type
     * 
     * @return true if sensitive, false otherwise
     */
    public boolean isSensitive() {
        return this == SENSITIVE;
    }

    /**
     * Check if this is a non-sensitive type
     * 
     * @return true if non-sensitive, false otherwise
     */
    public boolean isNonSensitive() {
        return this == NON_SENSITIVE;
    }

    /**
     * Check if this is a neutral type
     * 
     * @return true if neutral, false otherwise
     */
    public boolean isNeutral() {
        return this == NEUTRAL;
    }
}
