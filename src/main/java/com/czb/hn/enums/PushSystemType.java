package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * 功能模块推送类型
 */
public enum PushSystemType implements StandardEnum<Integer> {
    ALERT(1, "预警"),
    DAILY(2, "日报"),
    WEEKLY(3, "周报"),
    MONTHLY(4, "月报");

    private final Integer value;
    private final String description;

    PushSystemType(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Get enum value from string using smart conversion
     * 
     * @param value String value to convert
     * @return Corresponding enum value, or SYSTEM as default
     */
    public static PushSystemType fromString(String value) {
        return StandardEnum.smartConvertWithDefault(PushSystemType.class, value, null);
    }

    /**
     * Get enum value from string using smart conversion
     *
     * @param value String value to convert
     * @return Corresponding enum value, or SYSTEM as default
     */
    public static PushSystemType from(Integer value) {
        return StandardEnum.smartConvertWithDefault(PushSystemType.class, value, null);
    }

}
