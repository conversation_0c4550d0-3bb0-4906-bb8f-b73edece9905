package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Third Level Source Type Enumeration
 * 三级来源类型枚举 - String存储
 *
 * API字段: contentExt.originTypeThird
 * 存储方式: String存储 (API代码)
 * API值: mainlt, dylt, hylt, normallt, mainwd, normalwd, centerxwWb, 等三级分类代码
 */
public enum ThirdLevelSourceType implements StandardEnum<String> {
    // Forum Sources (论坛 lt)
    NATIONAL_KEY_FORUMS("mainlt", "全国重点论坛"),
    LOCAL_KEY_FORUMS("dylt", "地方重点论坛"),
    INDUSTRY_FORUMS("hylt", "行业论坛"),
    SMALL_MEDIUM_FORUMS("normallt", "中小论坛"),

    // Q&A Sources (问答 wenda)
    KEY_QA_PLATFORMS("mainwd", "重点问答"),
    OTHER_QA_PLATFORMS("normalwd", "其他问答"),

    // Weibo Media Accounts (微博媒体号 wbxw)
    WEIBO_CENTRAL_MEDIA("centerxwWb", "央级媒体号"),
    WEIBO_PROVINCIAL_MEDIA("proxwWb", "省级媒体号"),
    WEIBO_MUNICIPAL_MEDIA("areaxwWb", "地市媒体号"),
    WEIBO_COMMERCIAL_MEDIA("portalWb", "商业号"),
    WEIBO_OTHER_MEDIA("qitaWb", "其他媒体号"),

    // Weibo Government Accounts (微博政务号 wbzw)
    WEIBO_CENTRAL_GOVERNMENT("centerDepWb", "央级政府号"),
    WEIBO_PROVINCIAL_GOVERNMENT("proDepWb", "省级政府号"),
    WEIBO_MUNICIPAL_GOVERNMENT("cityDepWb", "市级政府号"),
    WEIBO_DISTRICT_GOVERNMENT("areaDepWb", "区县政府号"),
    WEIBO_OVERSEAS_INSTITUTIONS("jwDepWb", "驻境外机构号"),

    // WeChat Media Accounts (微信媒体号 wxxw)
    WECHAT_CENTRAL_MEDIA("centerxwWx", "央级媒体公众号"),
    WECHAT_PROVINCIAL_MEDIA("proxwWx", "省级媒体公众号"),
    WECHAT_MUNICIPAL_MEDIA("areaxwWx", "地市媒体公众号"),
    WECHAT_COMMERCIAL_MEDIA("portalWx", "商业媒体公众号"),

    // WeChat Government Accounts (微信政务号 wxzw)
    WECHAT_CENTRAL_GOVERNMENT("centerDepWx", "央级政府公众号"),
    WECHAT_PROVINCIAL_GOVERNMENT("proDepWx", "省级政府公众号"),
    WECHAT_MUNICIPAL_GOVERNMENT("cityDepWx", "市级政府公众号"),
    WECHAT_DISTRICT_GOVERNMENT("areaDepWx", "区县政府公众号"),
    WECHAT_OVERSEAS_INSTITUTIONS("jwDepWx", "驻境外机构公众号"),

    // Mobile Applications (APP app)
    NEWS_APPS("mainapp", "新闻APP"),
    GOVERNMENT_APPS("zwapp", "政务APP"),
    INDUSTRY_APPS("hyapp", "行业APP"),
    OTHER_APPS("appqita", "其他APP"),

    // Self Media (自媒体 zmt)
    NEWS_SELF_MEDIA("mainzmt", "新闻自媒体"),
    INDUSTRY_SELF_MEDIA("hyzmt", "行业自媒体"),
    OTHER_SELF_MEDIA("zmtqita", "其他自媒体"),

    // News Websites (新闻网站 xw)
    CENTRAL_NEWS_WEBSITES("centerXwweb", "央级媒体网站"),
    PROVINCIAL_NEWS_WEBSITES("proXwweb", "省级媒体网站"),
    MUNICIPAL_NEWS_WEBSITES("areaXwweb", "地市媒体网站"),
    COMMERCIAL_WEBSITES("portalweb", "商业网站"),

    // Government Websites (政务网站 zw)
    CENTRAL_GOVERNMENT_WEBSITES("centerDepwz", "央级政府网站"),
    PROVINCIAL_GOVERNMENT_WEBSITES("proDepWz", "省级政府网站"),
    MUNICIPAL_GOVERNMENT_WEBSITES("cityDepWz", "市级政府网站"),
    DISTRICT_GOVERNMENT_WEBSITES("areaDepWz", "区县政府网站"),
    OVERSEAS_INSTITUTIONS_WEBSITES("jwDepWz", "驻境外机构");

    private final String value;
    private final String description;

    ThirdLevelSourceType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from string value (matching API format)
     * 
     * @param value String value from API
     * @return Corresponding enum value
     */
    public static ThirdLevelSourceType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return OTHER_APPS; // Default fallback
        }

        String trimmedValue = value.trim();
        for (ThirdLevelSourceType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return OTHER_APPS; // Default fallback
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static ThirdLevelSourceType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return OTHER_APPS;
        }

        String trimmedValue = value.trim();
        for (ThirdLevelSourceType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return OTHER_APPS; // Default fallback
    }

    /**
     * Get the corresponding DetailedSourceType for this third level type
     * 
     * @return Corresponding DetailedSourceType
     */
    public DetailedSourceType getDetailedSourceType() {
        return switch (this) {
            case NATIONAL_KEY_FORUMS, LOCAL_KEY_FORUMS, INDUSTRY_FORUMS, SMALL_MEDIUM_FORUMS ->
                DetailedSourceType.FORUMS;
            case KEY_QA_PLATFORMS, OTHER_QA_PLATFORMS ->
                DetailedSourceType.QA_PLATFORMS;
            case WEIBO_CENTRAL_MEDIA, WEIBO_PROVINCIAL_MEDIA, WEIBO_MUNICIPAL_MEDIA, 
                 WEIBO_COMMERCIAL_MEDIA, WEIBO_OTHER_MEDIA ->
                DetailedSourceType.WEIBO_MEDIA_ACCOUNTS;
            case WEIBO_CENTRAL_GOVERNMENT, WEIBO_PROVINCIAL_GOVERNMENT, WEIBO_MUNICIPAL_GOVERNMENT,
                 WEIBO_DISTRICT_GOVERNMENT, WEIBO_OVERSEAS_INSTITUTIONS ->
                DetailedSourceType.WEIBO_GOVERNMENT_ACCOUNTS;
            case WECHAT_CENTRAL_MEDIA, WECHAT_PROVINCIAL_MEDIA, WECHAT_MUNICIPAL_MEDIA, 
                 WECHAT_COMMERCIAL_MEDIA ->
                DetailedSourceType.WECHAT_MEDIA_ACCOUNTS;
            case WECHAT_CENTRAL_GOVERNMENT, WECHAT_PROVINCIAL_GOVERNMENT, WECHAT_MUNICIPAL_GOVERNMENT,
                 WECHAT_DISTRICT_GOVERNMENT, WECHAT_OVERSEAS_INSTITUTIONS ->
                DetailedSourceType.WECHAT_GOVERNMENT_ACCOUNTS;
            case NEWS_APPS, GOVERNMENT_APPS, INDUSTRY_APPS, OTHER_APPS ->
                DetailedSourceType.MOBILE_APPLICATIONS;
            case NEWS_SELF_MEDIA, INDUSTRY_SELF_MEDIA, OTHER_SELF_MEDIA ->
                DetailedSourceType.SELF_MEDIA_PLATFORMS;
            case CENTRAL_NEWS_WEBSITES, PROVINCIAL_NEWS_WEBSITES, MUNICIPAL_NEWS_WEBSITES, 
                 COMMERCIAL_WEBSITES ->
                DetailedSourceType.NEWS_WEBSITES;
            case CENTRAL_GOVERNMENT_WEBSITES, PROVINCIAL_GOVERNMENT_WEBSITES, MUNICIPAL_GOVERNMENT_WEBSITES,
                 DISTRICT_GOVERNMENT_WEBSITES, OVERSEAS_INSTITUTIONS_WEBSITES ->
                DetailedSourceType.GOVERNMENT_WEBSITES;
        };
    }

    /**
     * Check if this is a government/official source
     * 
     * @return true if government source, false otherwise
     */
    public boolean isGovernmentSource() {
        return switch (this) {
            case WEIBO_CENTRAL_GOVERNMENT, WEIBO_PROVINCIAL_GOVERNMENT, WEIBO_MUNICIPAL_GOVERNMENT,
                 WEIBO_DISTRICT_GOVERNMENT, WEIBO_OVERSEAS_INSTITUTIONS,
                 WECHAT_CENTRAL_GOVERNMENT, WECHAT_PROVINCIAL_GOVERNMENT, WECHAT_MUNICIPAL_GOVERNMENT,
                 WECHAT_DISTRICT_GOVERNMENT, WECHAT_OVERSEAS_INSTITUTIONS,
                 GOVERNMENT_APPS,
                 CENTRAL_GOVERNMENT_WEBSITES, PROVINCIAL_GOVERNMENT_WEBSITES, MUNICIPAL_GOVERNMENT_WEBSITES,
                 DISTRICT_GOVERNMENT_WEBSITES, OVERSEAS_INSTITUTIONS_WEBSITES -> true;
            default -> false;
        };
    }

    /**
     * Check if this is a media organization source
     * 
     * @return true if media source, false otherwise
     */
    public boolean isMediaSource() {
        return switch (this) {
            case WEIBO_CENTRAL_MEDIA, WEIBO_PROVINCIAL_MEDIA, WEIBO_MUNICIPAL_MEDIA, WEIBO_OTHER_MEDIA,
                 WECHAT_CENTRAL_MEDIA, WECHAT_PROVINCIAL_MEDIA, WECHAT_MUNICIPAL_MEDIA,
                 NEWS_APPS, NEWS_SELF_MEDIA,
                 CENTRAL_NEWS_WEBSITES, PROVINCIAL_NEWS_WEBSITES, MUNICIPAL_NEWS_WEBSITES -> true;
            default -> false;
        };
    }

    /**
     * Check if this is a commercial source
     * 
     * @return true if commercial source, false otherwise
     */
    public boolean isCommercialSource() {
        return switch (this) {
            case WEIBO_COMMERCIAL_MEDIA, WECHAT_COMMERCIAL_MEDIA, COMMERCIAL_WEBSITES -> true;
            default -> false;
        };
    }

    /**
     * Get authority level (1-5, higher = more authoritative)
     * 
     * @return authority level
     */
    public int getAuthorityLevel() {
        return switch (this) {
            case WEIBO_CENTRAL_GOVERNMENT, WECHAT_CENTRAL_GOVERNMENT, CENTRAL_GOVERNMENT_WEBSITES,
                 CENTRAL_NEWS_WEBSITES -> 5; // Highest authority
            case WEIBO_PROVINCIAL_GOVERNMENT, WECHAT_PROVINCIAL_GOVERNMENT, PROVINCIAL_GOVERNMENT_WEBSITES,
                 PROVINCIAL_NEWS_WEBSITES -> 4;
            case WEIBO_MUNICIPAL_GOVERNMENT, WECHAT_MUNICIPAL_GOVERNMENT, MUNICIPAL_GOVERNMENT_WEBSITES,
                 MUNICIPAL_NEWS_WEBSITES -> 3;
            case WEIBO_DISTRICT_GOVERNMENT, WECHAT_DISTRICT_GOVERNMENT, DISTRICT_GOVERNMENT_WEBSITES,
                 NATIONAL_KEY_FORUMS, KEY_QA_PLATFORMS, NEWS_APPS, GOVERNMENT_APPS -> 2;
            default -> 1; // Lowest authority
        };
    }

    /**
     * Check if this is a key/important source
     * 
     * @return true if key source, false otherwise
     */
    public boolean isKeySource() {
        return switch (this) {
            case NATIONAL_KEY_FORUMS, KEY_QA_PLATFORMS,
                 WEIBO_CENTRAL_MEDIA, WEIBO_CENTRAL_GOVERNMENT,
                 WECHAT_CENTRAL_MEDIA, WECHAT_CENTRAL_GOVERNMENT,
                 NEWS_APPS, GOVERNMENT_APPS, NEWS_SELF_MEDIA,
                 CENTRAL_NEWS_WEBSITES, CENTRAL_GOVERNMENT_WEBSITES -> true;
            default -> false;
        };
    }

    @Override
    public String toString() {
        return description;
    }
}
