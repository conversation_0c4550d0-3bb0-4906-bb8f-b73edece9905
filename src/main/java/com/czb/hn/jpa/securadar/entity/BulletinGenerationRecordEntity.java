package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 简报生成记录实体类
 */
@Entity
@Getter
@Setter
@Table(name = "bulletin_generation_record")
public class BulletinGenerationRecordEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 简报标题
     */
    @Column(name = "bulletin_title", nullable = false)
    private String bulletinTitle;
    /**
     * 简报类型（日报、周报、月报）
     */
    @Column(name = "bulletin_type", nullable = false)
    private String bulletinType;

    /**
     * 任务开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 生成时间
     */
    @Column(name = "generation_time", nullable = false)
    private LocalDateTime generationTime;

    /**
     * 关联的任务ID
     */
    @Column(name = "job_id")
    private Long jobId;

    /**
     * 关联的方案ID
     */
    @Column(name = "plan_id")
    private Long planId;

    /**
     * 简报日期
     */
    @Column(name = "bulletin_date", nullable = false)
    private LocalDate bulletinDate;

    /**
     * MinIO中的文件对象名称
     */
    @Column(name = "file_object_name")
    private String fileObjectName;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 状态（生成成功、生成失败、已发送、发送失败）
     */
    @Column(name = "status", nullable = false)
    private String status;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 是否已删除（逻辑删除）
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}