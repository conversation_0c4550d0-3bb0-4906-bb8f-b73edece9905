package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "monitor_configurations_test", indexes = {
        @Index(name = "idx_plan_id", columnList = "plan_id")
})
public class MonitorConfiguration {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "plan_id", nullable = false)
    private Long planId;

    @Column(name = "time", nullable = false)
    private Integer time;

    @Column(name = "start_time")
    private String startTime;

    @Column(name = "end_time")
    private String endTime;

    @Column(name = "sort_rule", nullable = false)
    private Integer sortRule;

    @Column(name = "sensitivity_type")
    private Integer sensitivityType;

    @Column(name = "similarity_display_rule", nullable = false)
    private Boolean similarityDisplayRule;

    @Column(name = "match_method", nullable = false)
    private Integer matchMethod;

    @Column(name = "media_type")
    private String mediaType;

    @Column(name = "media_type_second")
    private String mediaTypeSecond;

    @Column(name = "media_type_third")
    private String mediaTypeThird;

    @Column(name = "content_type")
    private String contentType;

    @Column(name = "is_original")
    private String isOriginal;

    @Column(name = "image_text_mode")
    private Integer imageTextMode;

    @Column(name = "second_trades")
    private String secondTrades;

    @Column(name = "author_followers_count_min")
    private Long authorFollowersCountMin;

    @Column(name = "author_followers_count_max")
    private Long authorFollowersCountMax;

    @Column(name = "media_level")
    private String mediaLevel;

    /**
     * Creation timestamp
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * Last update timestamp
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
