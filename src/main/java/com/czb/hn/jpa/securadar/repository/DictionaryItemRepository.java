package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.DictionaryItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;



import java.util.List;
import java.util.Optional;

@Repository
public interface DictionaryItemRepository extends JpaRepository<DictionaryItem, Long> {

    List<DictionaryItem> findByItemType(String itemType);

    List<DictionaryItem> findByItemTypeIn(List<String> itemTypes);

    @Query("select di.itemType FROM DictionaryItem as di group by di.itemType")
    List<String> findDistinctItemType();

    Optional<DictionaryItem> findByItemTypeAndItemValue(String itemType, String itemValue);
}
