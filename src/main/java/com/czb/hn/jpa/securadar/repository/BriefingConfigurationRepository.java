package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.BriefingConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BriefingConfigurationRepository extends JpaRepository<BriefingConfiguration, Long> {

    /**
     * Find briefing configurations by plan ID
     */
    @Query("SELECT bc FROM BriefingConfiguration bc WHERE bc.planId = :planId")
    BriefingConfiguration findByPlanId(Long planId);

    /**
     * Check if a briefing configuration with the same plan ID exists
     */
    @Query("SELECT COUNT(bc) > 0 FROM BriefingConfiguration bc WHERE bc.planId = :planId ")
    Boolean existsByPlanId(Long planId);
}
