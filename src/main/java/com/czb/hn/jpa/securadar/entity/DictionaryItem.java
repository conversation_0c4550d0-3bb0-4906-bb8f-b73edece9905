package com.czb.hn.jpa.securadar.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.UniqueConstraint;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_dictionary_item", indexes = {
        @Index(name = "idx_item_type", columnList = "item_type"),
}, uniqueConstraints = {
        @UniqueConstraint(name = "idx_item_type_item_value", columnNames = "item_type,item_value")
})
public class DictionaryItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Comment("字典项类型")
    @Column(name = "item_type", nullable = false, columnDefinition = "varchar(100)")
    private String itemType;   // 字典项类型，例如 enterprise_type, organization_type

    @Comment("字典项名称")
    @Column(name = "item_name", nullable = false, columnDefinition = "varchar(100)")
    private String itemName;   // 字典项名称，

    @Comment("字典项值")
    @Column(name = "item_value", nullable = false, columnDefinition = "varchar(100)")
    private String itemValue;  // 字典项值，如类型代码或标识符

    @Comment("字典项描述")
    @Column(name = "description", columnDefinition = "varchar(100)")
    private String description; // 可选的描述信息


    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
