package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.entity.Recipients;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecipientsListRepository extends JpaRepository<Recipients, Long> {
    /**
     *  Find recipients list by plan id, system type and receive type
     */
    @Query("SELECT rl FROM Recipients rl WHERE rl.planId = :planId AND rl.systemType = :systemType AND rl.receiveType = :receiveType")
    List<Recipients> findByPlanIdAndSystemTypeAndReceiveType(Long planId, Integer systemType, PushType receiveType);
}
