package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 简报生成记录数据访问接口
 */
@Repository
public interface BulletinGenerationRecordRepository extends JpaRepository<BulletinGenerationRecordEntity, Long> {

    /**
     * 根据任务ID查询生成记录
     *
     * @param jobId 任务ID
     * @return 生成记录列表
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.jobId = :jobId AND b.isDeleted = false")
    List<BulletinGenerationRecordEntity> findByJobId(@Param("jobId") Long jobId);

    /**
     * 根据任务ID查询生成记录（分页）
     *
     * @param jobId    任务ID
     * @param pageable 分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.jobId = :jobId AND b.isDeleted = false")
    Page<BulletinGenerationRecordEntity> findByJobId(@Param("jobId") Long jobId, Pageable pageable);

    /**
     * 根据方案ID查询生成记录
     *
     * @param planId 方案ID
     * @return 生成记录列表
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.planId = :planId AND b.isDeleted = false")
    List<BulletinGenerationRecordEntity> findByPlanId(@Param("planId") Long planId);

    /**
     * 根据方案ID查询生成记录（分页）
     *
     * @param planId   方案ID
     * @param pageable 分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.planId = :planId AND b.isDeleted = false")
    Page<BulletinGenerationRecordEntity> findByPlanId(@Param("planId") Long planId, Pageable pageable);

    /**
     * 根据生成时间范围查询生成记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 生成记录列表
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.generationTime BETWEEN :startTime AND :endTime AND b.isDeleted = false ORDER BY b.generationTime DESC")
    List<BulletinGenerationRecordEntity> findByGenerationTimeBetween(@Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 根据生成时间范围查询生成记录（分页）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageable  分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.generationTime BETWEEN :startTime AND :endTime AND b.isDeleted = false ORDER BY b.generationTime DESC")
    Page<BulletinGenerationRecordEntity> findByGenerationTimeBetween(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);

    /**
     * 根据简报类型查询生成记录
     *
     * @param bulletinType 简报类型
     * @return 生成记录列表
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.bulletinType = :bulletinType AND b.isDeleted = false")
    List<BulletinGenerationRecordEntity> findByBulletinType(@Param("bulletinType") String bulletinType);

    /**
     * 根据简报类型查询生成记录（分页）
     *
     * @param bulletinType 简报类型
     * @param pageable     分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.bulletinType = :bulletinType AND b.isDeleted = false")
    Page<BulletinGenerationRecordEntity> findByBulletinType(String bulletinType, Pageable pageable);

    /**
     * 查询最近的生成记录
     *
     * @param pageable 分页参数
     * @return 生成记录列表
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.isDeleted = false ORDER BY b.generationTime DESC")
    List<BulletinGenerationRecordEntity> findRecentRecords(Pageable pageable);

    /**
     * 根据任务ID和简报日期查询生成记录
     *
     * @param jobId        任务ID
     * @param bulletinDate 简报日期
     * @return 生成记录
     */
    Optional<BulletinGenerationRecordEntity> findByJobIdAndBulletinDate(Long jobId, LocalDate bulletinDate);

    /**
     * 根据任务ID、简报日期和状态查询生成记录
     *
     * @param jobId        任务ID
     * @param bulletinDate 简报日期
     * @param status       状态
     * @return 生成记录
     */
    Optional<BulletinGenerationRecordEntity> findByJobIdAndBulletinDateAndStatus(Long jobId, LocalDate bulletinDate,
            String status);

    /**
     * 查询指定日期范围内未发送的简报
     *
     * @param jobId     任务ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 未发送的简报列表
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.jobId = :jobId AND b.bulletinDate BETWEEN :startDate AND :endDate AND b.status = 'SUCCESS' ORDER BY b.bulletinDate")
    List<BulletinGenerationRecordEntity> findUnsentBulletins(
            @Param("jobId") Long jobId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    /**
     * 根据时间范围和简报类型查询生成记录（分页）
     * 如果bulletinType为null，则只按时间范围查询
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param bulletinType 简报类型（可选）
     * @param pageable     分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.generationTime BETWEEN :startTime AND :endTime AND (:bulletinType IS NULL OR b.bulletinType = :bulletinType) AND b.isDeleted = false ORDER BY b.generationTime DESC")
    Page<BulletinGenerationRecordEntity> findByGenerationTimeBetweenAndBulletinType(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("bulletinType") String bulletinType,
            Pageable pageable);

    /**
     * 根据方案ID和时间范围查询生成记录（分页）
     *
     * @param planId    方案ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageable  分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.planId = :planId AND b.generationTime BETWEEN :startTime AND :endTime AND b.isDeleted = false ORDER BY b.generationTime DESC")
    Page<BulletinGenerationRecordEntity> findByPlanIdAndGenerationTimeBetween(
            @Param("planId") Long planId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);

    /**
     * 根据方案ID、时间范围和简报类型查询生成记录（分页）
     * 如果bulletinType为null，则只按方案ID和时间范围查询
     *
     * @param planId       方案ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param bulletinType 简报类型（可选）
     * @param pageable     分页参数
     * @return 生成记录分页结果
     */
    @Query("SELECT b FROM BulletinGenerationRecordEntity b WHERE b.planId = :planId AND b.generationTime BETWEEN :startTime AND :endTime AND (:bulletinType IS NULL OR b.bulletinType = :bulletinType) AND b.isDeleted = false ORDER BY b.generationTime DESC")
    Page<BulletinGenerationRecordEntity> findByPlanIdAndGenerationTimeBetweenAndBulletinType(
            @Param("planId") Long planId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("bulletinType") String bulletinType,
            Pageable pageable);

    // 逻辑删除单条
    @Modifying
    @Query("UPDATE BulletinGenerationRecordEntity b SET b.isDeleted = true WHERE b.id = :id")
    void logicDeleteById(@Param("id") Long id);

    // 逻辑删除多条
    @Modifying
    @Query("UPDATE BulletinGenerationRecordEntity b SET b.isDeleted = true WHERE b.id IN :ids")
    void logicDeleteByIdIn(@Param("ids") List<Long> ids);
}