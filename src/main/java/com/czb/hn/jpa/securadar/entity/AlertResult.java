package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.converter.ContentCategoryConverter;
import com.czb.hn.enums.converter.ContentMatchTypeConverter;
import com.czb.hn.enums.converter.ContentTypeConverter;
import com.czb.hn.enums.converter.InformationSensitivityTypeConverter;
import com.czb.hn.enums.converter.MediaLevelConverter;
import com.czb.hn.enums.converter.SourceTypeConverter;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Alert Result Entity
 * Stores alert processing results with data sourced from Elasticsearch
 * Supports multi-tenant isolation and flexible schema design
 */
@Entity
@Table(name = "alert_results", indexes = {
        @Index(name = "idx_enterprise_warning_time", columnList = "enterprise_id,warning_time"),
        @Index(name = "idx_plan_warning_time", columnList = "plan_id,warning_time"),
        @Index(name = "idx_configuration_time", columnList = "configuration_id,warning_time"),
        @Index(name = "idx_warning_level", columnList = "warning_level"),
        @Index(name = "idx_information_sensitivity_type", columnList = "information_sensitivity_type"),
        @Index(name = "idx_content_category", columnList = "content_category"),
        @Index(name = "idx_source", columnList = "source"),
        @Index(name = "idx_original_content", columnList = "original_content_id"),
        @Index(name = "idx_enterprise_sensitive_level", columnList = "enterprise_id,information_sensitivity_type,warning_level"),
        @Index(name = "idx_plan_category_time", columnList = "plan_id,content_category,warning_time"),
        @Index(name = "idx_enterprise_source_time", columnList = "enterprise_id,source,warning_time"),
        // New indexes for additional fields
        @Index(name = "idx_source_type", columnList = "source_type"),
        @Index(name = "idx_content_type", columnList = "content_type"),
        @Index(name = "idx_content_match_type", columnList = "content_match_type"),
        @Index(name = "idx_media_level", columnList = "media_level"),
        @Index(name = "idx_provincial", columnList = "provincial"),
        @Index(name = "idx_enterprise_source_type_time", columnList = "enterprise_id,source_type,warning_time"),
        @Index(name = "idx_plan_media_level_time", columnList = "plan_id,media_level,warning_time"),
        @Index(name = "idx_content_type_sensitivity", columnList = "content_type,information_sensitivity_type")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlertResult {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant isolation fields
    @Column(name = "enterprise_id", nullable = false, length = 255)
    private String enterpriseId;

    @Column(name = "plan_id")
    private Long planId;

    @Column(name = "configuration_id", nullable = false)
    private Long configurationId;

    // Alert core data
    @Column(nullable = false, columnDefinition = "TEXT")
    private String title;

    @Column(nullable = false, columnDefinition = "LONGTEXT")
    private String content;

    @Column(name = "involved_keywords", columnDefinition = "JSON", nullable = false)
    private String involvedKeywords;

    @Convert(converter = InformationSensitivityTypeConverter.class)
    @Column(name = "information_sensitivity_type", nullable = false)
    @Builder.Default
    private InformationSensitivityType informationSensitivityType = InformationSensitivityType.NEUTRAL;

    @Convert(converter = SourceTypeConverter.class)
    @Column(name = "source_type", nullable = false)
    @Builder.Default
    private SourceType sourceType = SourceType.WEIBO;

    @Convert(converter = ContentTypeConverter.class)
    @Column(name = "content_type", nullable = false)
    @Builder.Default
    private ContentType contentType = ContentType.TEXT;

    @Convert(converter = ContentMatchTypeConverter.class)
    @Column(name = "content_match_type", nullable = false)
    @Builder.Default
    private ContentMatchType contentMatchType = ContentMatchType.MAIN_TXT;

    @Convert(converter = ContentCategoryConverter.class)
    @Column(name = "content_category", nullable = false)
    @Builder.Default
    private ContentCategory contentCategory = ContentCategory.ORIGINAL;

    @Convert(converter = MediaLevelConverter.class)
    @Column(name = "media_level", nullable = false)
    @Builder.Default
    private MediaLevel mediaLevel = MediaLevel.SMALL_MEDIUM;

    @Enumerated(EnumType.STRING)
    @Column(name = "warning_level", nullable = true)
    @Builder.Default
    private WarningLevel warningLevel = null;

    @Column(nullable = false, length = 255)
    private String source;

    @Column(nullable = false, length = 32)
    private String provincial;

    @Column(name = "warning_time", nullable = false, columnDefinition = "TIMESTAMP(0)")
    private LocalDateTime warningTime;

    @Column(name = "similar_article_count")
    @Builder.Default
    private Integer similarArticleCount = 0;

    // Source data reference (ALL from Elasticsearch)
    @Column(name = "original_content_id", nullable = false, length = 64)
    private String originalContentId;

    @Column(name = "alert_push_id")
    private Long alertPushId;

    // Flexible extension
    @Column(name = "extended_attributes", columnDefinition = "JSON")
    private String extendedAttributes;

    // Processing metadata
    @Column(name = "processing_version", length = 20)
    @Builder.Default
    private String processingVersion = "1.0";

    @Column(name = "rule_snapshot", columnDefinition = "JSON")
    private String ruleSnapshot;

    // Notification and push associations
    @Column(name = "alert_notification_queue_id")
    private Long alertNotificationQueueId;

    @Column(name = "alert_push_detail_id")
    private Long alertPushDetailId;

    // Audit fields
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by", length = 255)
    private String createdBy;

    /**
     * Warning level enumeration - matches LevelSettingsDto terminology
     */
    public enum WarningLevel {
        GENERAL("一般"),
        MODERATE("中等"),
        SEVERE("严重");

        private final String description;

        WarningLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
