package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.enums.EmotionType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.jpa.securadar.entity.SinaNewsDwdEntity;
import com.czb.hn.enums.SourceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 新浪舆情DWD数据仓库接口
 */
@Repository
public interface SinaNewsDwdRepository extends JpaRepository<SinaNewsDwdEntity, Long> {

    /**
     * 根据内容ID查找记录
     * 
     * @param contentId 内容ID
     * @return DWD记录
     */
    Optional<SinaNewsDwdEntity> findByContentId(String contentId);

    /**
     * 根据发布日期查找记录
     * 
     * @param date 发布日期
     * @return DWD记录列表
     */
    @Query("SELECT d FROM SinaNewsDwdEntity d WHERE CAST(d.publishTime AS LocalDate) = :date")
    List<SinaNewsDwdEntity> findByPublishDate(@Param("date") LocalDate date);

    /**
     * 根据发布时间范围查找记录
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return DWD记录列表
     */
    List<SinaNewsDwdEntity> findByPublishTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据媒体类型查找记录
     *
     * @param mediaType 媒体类型
     * @return DWD记录列表
     */
    List<SinaNewsDwdEntity> findByMediaType(SourceType mediaType);

    /**
     * 根据情感类型查找记录
     *
     * @param emotion 情感类型
     * @return DWD记录列表
     */
    List<SinaNewsDwdEntity> findByEmotion(EmotionType emotion);

    /**
     * 查找未处理到DWS的记录
     * 
     * @return 未处理的DWD记录列表
     */
    @Query("SELECT d FROM SinaNewsDwdEntity d WHERE d.processedToDws = false ORDER BY d.publishTime ASC")
    List<SinaNewsDwdEntity> findUnprocessedToDwsRecords();

    /**
     * 统计未处理到DWS的记录数
     * 
     * @return 未处理的记录数
     */
    @Query("SELECT COUNT(d) FROM SinaNewsDwdEntity d WHERE d.processedToDws = false")
    long countUnprocessedToDwsRecords();


}