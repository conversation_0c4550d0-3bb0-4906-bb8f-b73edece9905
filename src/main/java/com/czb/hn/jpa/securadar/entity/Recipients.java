package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.PushType;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "recipients", indexes = {
        @Index(name = "idx_plan_id", columnList = "plan_id"),
        @Index(name = "idx_system_type", columnList = "system_type"),
        @Index(name = "idx_receive_type", columnList = "receive_type")
})
public class Recipients {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "plan_id", nullable = false)
    private Long planId;

    @Column(name = "system_type", nullable = false)
    private Integer systemType;

    @Enumerated(EnumType.STRING)
    @Column(name = "receive_type", nullable = false)
    private PushType receiveType;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "address", nullable = false)
    private String address;

    @Column(name = "enable", nullable = false)
    private boolean enable;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updateAt;
}
