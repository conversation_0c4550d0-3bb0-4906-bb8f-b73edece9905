信息属性    InformationSensitivityType
匹配方式    ContentMatchType
时间类型    TimeType  
信息排序    SortRule
相似文章    SimilarArticleMerage 
来源类型（数字报 横排）           SourceType

互动论坛  INTERACTIVE_FORUMS
    留言版 liuyanban
    投诉爆料 tousu
论坛 lt
    "全国重点论坛
    （mainlt）"
    "地方重点论坛
    （dylt）"
    "行业论坛
    （hylt）"
    "中小论坛
    （normallt）"
问答 wenda
    "重点问答
    （mainwd）"
    "其他问答
    （normalwd）"
    贴吧 tieba
    博客 blog
微博    WEIBO
微博媒体号 wbxw
    "央级媒体号
    （centerxwWb）"
    "省级媒体号
    （proxwWb）"
    "地市媒体号
    （areaxwWb）"
    "商业号
    （portalWb）"
    "其他媒体号
    （qitaWb）"
微博政务号 wbzw
    "央级政府号
    （centerDepWb）"
    "省级政府号
    （proDepWb）"
    "市级政府号
    （cityDepWb）"
    "区县政府号
    （areaDepWb）"
    "驻境外机构号
    （jwDepWb）"
    微博校园号wbschool
    微博团体号wbteam
    微博企业号wbcompany
    微博网站号wbweb
    金v号wbgoldv
微信   WECHAT
微信媒体号 wxxw
    "央级媒体公众号
    （centerxwWx）"
    "省级媒体公众号
    （proxwWx）"
    "地市媒体公众号
    （areaxwWx）"
    "商业媒体公众号
    （portalWx）"
微信政务号 wxzw
    "央级政府公众号
    （centerDepWx）"
    "省级政府公众号
    （proDepWx）"
    "市级政府公众号
    （cityDepWx）"
    "区县政府公众号
    （areaDepWx）"
    "驻境外机构公众号
    （jwDepWx）"
    微信机构号 wxorg
    微信企业号 wxcompany
    微信个人号 wxpersonal
客户端  MOBILE_APPS
APP app
    "新闻APP
    （mainapp）"
    "政务APP
    （zwapp）"
    "行业APP
    （hyapp）"
    "其他APP
    （appqita）"
自媒体 zmt
    "新闻自媒体
    （mainzmt）"
    "行业自媒体
    （hyzmt）"
    "其他自媒体
    （zmtqita）"
视频    VIDEO
    短视频 xsp
    视频网站 spwz
    电视视频 diantai
数字报  DIGITAL_NEWSPAPERS
    央级数字报 szbyj
    省级数字报 szbsj
    地市数字报 szbdj
    其他数字报 szbqita
网站    WEBSITES
新闻网站 xw
    "央级媒体网站
    （centerXwweb）"
    "省级媒体网站
    （proXwweb）"
    "地市媒体网站
    （areaXwweb）"
    "商业网站
    （portalweb）"

政务网站 zw
    "央级政府网站
    （centerDepwz）"
    "省级政府网站
    （proDepWz）"
    "市级政府网站
    （cityDepWz）"
    "区县政府网站
    （areaDepWz）"
    "驻境外机构
    （jwDepWz）"
    行业网站 wzhangye
    机构组织 wzorg
    企业官网 wzqy
    境外网媒 waimei 
    其他网站 wzqita
信源级别  MediaLevel
内容包含 ContentType
内容类型 ContentCategory

图文识别  ImageTextRecognition
行业分类  IndustryCategory
    政务；公安；司法；教育；能源环保；房产；医疗；金融；招标；汽车；科技；军事；游戏；娱乐；体育；母婴；文化；时尚；生活；彩票；旅游；通信；电商；其他

推送系统类型  PushSystemType

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '全部', 'ALL', 'InformationSensitivityType', '0', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '按全文', 'fulltext', 'ContentMatchType', '0', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '全部', 'ALL', 'ImageTextRecognition', '0', '2025-07-09 08:41:08.000000');


INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '预警', 'alert', 'PushSystemType', '1', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '日报', 'daily', 'PushSystemType', '2', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '周报', 'weekly', 'PushSystemType', '3', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '月报', 'monthly', 'PushSystemType', '4', '2025-07-09 08:41:08.000000');









INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '仅看图文', 'only_image_text', 'ImageTextRecognition', '1', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '排除图文', 'exclude_image_text', 'ImageTextRecognition', '2', '2025-07-09 08:41:08.000000');

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '政务', '政务', 'IndustryCategory', '政务', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '公安', '公安', 'IndustryCategory', '公安', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '司法', '司法', 'IndustryCategory', '司法', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '教育', '教育', 'IndustryCategory', '教育', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '能源环保', '能源环保', 'IndustryCategory', '能源环保', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '房产', '房产', 'IndustryCategory', '房产', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '医疗', '医疗', 'IndustryCategory', '医疗', '2025-07-09 08:41:08.000000'); 
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '金融', '金融', 'IndustryCategory', '金融', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '招标', '招标', 'IndustryCategory', '招标', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '汽车', '汽车', 'IndustryCategory', '汽车', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '科技', '科技', 'IndustryCategory', '科技', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '军事', '军事', 'IndustryCategory', '军事', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '游戏', '游戏', 'IndustryCategory', '游戏', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '娱乐', '娱乐', 'IndustryCategory', '娱乐', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '体育', '体育', 'IndustryCategory', '体育', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '母婴', '母婴', 'IndustryCategory', '母婴', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '文化', '文化', 'IndustryCategory', '文化', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '时尚', '时尚', 'IndustryCategory', '时尚', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '生活', '生活', 'IndustryCategory', '生活', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '彩票', '彩票', 'IndustryCategory', '彩票', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '旅游', '旅游', 'IndustryCategory', '旅游', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '通信', '通信', 'IndustryCategory', '通信', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '电商', '电商', 'IndustryCategory', '电商', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '其他', 'qita', 'IndustryCategory', 'qita', '2025-07-09 08:41:08.000000');





INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '留言板', 'liuyanban', 'INTERACTIVE_FORUMS', 'liuyanban', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '投诉爆料', 'tousu', 'INTERACTIVE_FORUMS', 'tousu', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '论坛', 'lt', 'INTERACTIVE_FORUMS', 'lt', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '问答', 'wenda', 'INTERACTIVE_FORUMS', 'wenda', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '贴吧', 'tieba', 'INTERACTIVE_FORUMS', 'tieba', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '博客', 'blog', 'INTERACTIVE_FORUMS', 'blog', '2025-07-09 08:41:08.000000');

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微博媒体号', 'wbxw', 'WEIBO', 'wbxw', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微博政务号', 'wbzw', 'WEIBO', 'wbzw', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微博校园号', 'wbschool', 'WEIBO', 'wbschool', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微博团体号', 'wbteam', 'WEIBO', 'wbteam', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微博企业号', 'wbcompany', 'WEIBO', 'wbcompany', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微博网站号', 'wbweb', 'WEIBO', 'wbweb', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '金v号', 'wbgoldv', 'WEIBO', 'wbgoldv', '2025-07-09 08:41:08.000000');


INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微信媒体号', 'wxxw', 'WECHAT', 'wxxw', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微信政务号', 'wxzw', 'WECHAT', 'wxzw', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微信机构号', 'wxorg', 'WECHAT', 'wxorg', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微信企业号', 'wxcompany', 'WECHAT', 'wxcompany', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '微信个人号', 'wxpersonal', 'WECHAT', 'wxpersonal', '2025-07-09 08:41:08.000000');

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', 'APP', 'app', 'MOBILE_APPS', 'app', '2025-07-09 08:41:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '自媒体', 'zmt', 'MOBILE_APPS', 'zmt', '2025-07-09 08:41:08.000000');

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '短视频', 'xsp', 'VIDEO', 'xsp', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '视频网站', 'spwz', 'VIDEO', 'spwz', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '电视视频', 'diantai', 'VIDEO', 'diantai', '2025-07-09 08:41:08.000000');

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '央级数字报', 'szbyj', 'DIGITAL_NEWSPAPERS', 'szbyj', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '省级数字报', 'szbsj', 'DIGITAL_NEWSPAPERS', 'szbsj', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '地市数字报', 'szbdj', 'DIGITAL_NEWSPAPERS', 'szbdj', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '其他数字报', 'szbqita', 'DIGITAL_NEWSPAPERS', 'szbqita', '2025-07-09 08:41:08.000000');

INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '新闻网站', 'xw', 'WEBSITES', 'xw', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '政务网站', 'zw', 'WEBSITES', 'zw', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '行业网站', 'wzhangye', 'WEBSITES', 'wzhangye', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '机构组织', 'wzorg', 'WEBSITES', 'wzorg', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '企业官网', 'wzqy', 'WEBSITES', 'wzqy', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '境外网媒', 'waimei', 'WEBSITES', 'waimei', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '其他网站', 'wzqita', 'WEBSITES', 'wzqita', '2025-07-09 08:41:08.000000');



INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '今天', 'today', 'TimeType', '1', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '24小时', '24hours', 'TimeType', '2', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '7天', '7days', 'TimeType', '3', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '自定义', 'custom', 'TimeType', '4', '2025-07-09 08:41:08.000000');


INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '时间逆序', 'timedescending', 'SortRule', '1', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '时间顺序', 'timeascending', 'SortRule', '2', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '相似文章数', 'similaritynumdescending', 'SortRule', '3', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '阅读数降序', 'readnumdescending', 'SortRule', '4', '2025-07-09 08:41:08.000000');


INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '合并', 'yes', 'SimilarArticleMerage', '1', '2025-07-09 08:41:08.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-09 08:41:08.000000', '不合并', 'no', 'SimilarArticleMerage', '2', '2025-07-09 08:41:08.000000');

