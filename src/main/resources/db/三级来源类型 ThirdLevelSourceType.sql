-- 三级来源类型 ThirdLevelSourceType
-- 基于DetailedSourceType的进一步细分，提供更精确的来源分类

-- 论坛类 (lt)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '全国重点论坛', 'NATIONAL_KEY_FORUMS', 'LT', 'mainlt', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '地方重点论坛', 'LOCAL_KEY_FORUMS', 'LT', 'dylt', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '行业论坛', 'INDUSTRY_FORUMS', 'LT', 'hylt', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '中小论坛', 'SMALL_MEDIUM_FORUMS', 'LT', 'normallt', '2025-07-11 00:00:00.000000');


-- 问答类 (wenda)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '重点问答', 'KEY_QA_PLATFORMS', 'WENDA', 'mainwd', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '其他问答', 'OTHER_QA_PLATFORMS', 'WENDA', 'normalwd', '2025-07-11 00:00:00.000000');

-- 微博媒体号类 (wbxw)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '央级媒体号', 'WEIBO_CENTRAL_MEDIA', 'WBXW', 'centerxwWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '省级媒体号', 'WEIBO_PROVINCIAL_MEDIA', 'WBXW', 'proxwWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '地市媒体号', 'WEIBO_MUNICIPAL_MEDIA', 'WBXW', 'areaxwWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '商业号', 'WEIBO_COMMERCIAL_MEDIA', 'WBXW', 'portalWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '其他媒体号', 'WEIBO_OTHER_MEDIA', 'WBXW', 'qitaWb', '2025-07-11 00:00:00.000000');

-- 微博政务号类 (wbzw)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '央级政府号', 'WEIBO_CENTRAL_GOVERNMENT', 'WBZW', 'centerDepWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '省级政府号', 'WEIBO_PROVINCIAL_GOVERNMENT', 'WBZW', 'proDepWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '市级政府号', 'WEIBO_MUNICIPAL_GOVERNMENT', 'WBZW', 'cityDepWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '区县政府号', 'WEIBO_DISTRICT_GOVERNMENT', 'WBZW', 'areaDepWb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '驻境外机构号', 'WEIBO_OVERSEAS_INSTITUTIONS', 'WBZW', 'jwDepWb', '2025-07-11 00:00:00.000000');

-- 微信媒体号类 (wxxw)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '央级媒体公众号', 'WECHAT_CENTRAL_MEDIA', 'WXXW', 'centerxwWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '省级媒体公众号', 'WECHAT_PROVINCIAL_MEDIA', 'WXXW', 'proxwWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '地市媒体公众号', 'WECHAT_MUNICIPAL_MEDIA', 'WXXW', 'areaxwWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '商业媒体公众号', 'WECHAT_COMMERCIAL_MEDIA', 'WXXW', 'portalWx', '2025-07-11 00:00:00.000000');

-- 微信政务号类 (wxzw)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '央级政府公众号', 'WECHAT_CENTRAL_GOVERNMENT', 'WXZW', 'centerDepWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '省级政府公众号', 'WECHAT_PROVINCIAL_GOVERNMENT', 'WXZW', 'proDepWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '市级政府公众号', 'WECHAT_MUNICIPAL_GOVERNMENT', 'WXZW', 'cityDepWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '区县政府公众号', 'WECHAT_DISTRICT_GOVERNMENT', 'WXZW', 'areaDepWx', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '驻境外机构公众号', 'WECHAT_OVERSEAS_INSTITUTIONS', 'WXZW', 'jwDepWx', '2025-07-11 00:00:00.000000');

-- APP类 (app)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '新闻APP', 'NEWS_APPS', 'APP', 'mainapp', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '政务APP', 'GOVERNMENT_APPS', 'APP', 'zwapp', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '行业APP', 'INDUSTRY_APPS', 'APP', 'hyapp', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '其他APP', 'OTHER_APPS', 'APP', 'appqita', '2025-07-11 00:00:00.000000');

-- 自媒体类 (zmt)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '新闻自媒体', 'NEWS_SELF_MEDIA', 'ZMT', 'mainzmt', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '行业自媒体', 'INDUSTRY_SELF_MEDIA', 'ZMT', 'hyzmt', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '其他自媒体', 'OTHER_SELF_MEDIA', 'ZMT', 'zmtqita', '2025-07-11 00:00:00.000000');

-- 新闻网站类 (xw)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '央级媒体网站', 'CENTRAL_NEWS_WEBSITES', 'XW', 'centerXwweb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '省级媒体网站', 'PROVINCIAL_NEWS_WEBSITES', 'XW', 'proXwweb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '地市媒体网站', 'MUNICIPAL_NEWS_WEBSITES', 'XW', 'areaXwweb', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '商业网站', 'COMMERCIAL_WEBSITES', 'XW', 'portalweb', '2025-07-11 00:00:00.000000');

-- 政务网站类 (zw)
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '央级政府网站', 'CENTRAL_GOVERNMENT_WEBSITES', 'ZW', 'centerDepwz', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '省级政府网站', 'PROVINCIAL_GOVERNMENT_WEBSITES', 'ZW', 'proDepWz', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '市级政府网站', 'MUNICIPAL_GOVERNMENT_WEBSITES', 'ZW', 'cityDepWz', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '区县政府网站', 'DISTRICT_GOVERNMENT_WEBSITES', 'ZW', 'areaDepWz', '2025-07-11 00:00:00.000000');
INSERT INTO `securadar`.`t_dictionary_item` (`created_at`, `description`, `item_name`, `item_type`, `item_value`, `updated_at`) VALUES ('2025-07-11 00:00:00.000000', '驻境外机构', 'OVERSEAS_INSTITUTIONS_WEBSITES', 'ZW', 'jwDepWz', '2025-07-11 00:00:00.000000');


-- 论坛类 (lt)
-- 问答类 (wenda)
-- 微博媒体号类 (wbxw)
-- 微博政务号类 (wbzw)
-- 微信媒体号类 (wxxw)
-- 微信政务号类 (wxzw)
-- APP类 (app)
-- 自媒体类 (zmt)
-- 新闻网站类 (xw)
-- 政务网站类 (zw)