# 测试环境配置
schedule:
  collector:
    enabled: false # 测试环境禁用数据收集
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
    batch-size: 10 # 测试环境很小的批处理大小
    max-retries: 1 # 最少的重试次数
    retry-interval: 1000 # 最短的重试间隔
  cleaner:
    enabled: false # 测试环境禁用数据清洗
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
    batch-size: 10 # 很小的批处理大小
  aggregator:
    enabled: false # 测试环境禁用数据聚合
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
  elasticsearch-sync:
    enabled: false # 测试环境禁用ES同步
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
  billing:
    enabled: false # 测试环境禁用计费系统定时任务
    expiration-check-cron: "-" # 禁用定时任务
    expiration-warning-cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
    warning-days: 1 # 测试环境1天警告
    batch-size: 10 # 很小的批处理大小

# 测试环境日志配置
logging:
  level:
    root: WARN # 测试环境减少日志输出
    com.czb.hn: INFO
    org.springframework.scheduling: WARN # 减少定时任务日志

# 禁用外部服务
sina:
  api:
    enabled: false # 测试环境禁用新浪API

elasticsearch:
  enabled: false # 测试环境禁用Elasticsearch

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    disable-swagger-default-url: false
    displayRequestDuration: true
    enabled: true
  api-docs:
    path: /v3/api-docs
    enabled: true

onepass:
  issuer: ${ONEPASS_ISSUER:https://onepass-auth-server-ycjf-test.ycfin.net/realm/f7c125e9a0ae4399b98d1d1f2bb9ab76}
  client-id: ${ONEPASS_CLIENT_ID:0407725a7a7f4cf984043da156f7b3cd}
  client-secret: ${ONEPASS_CLIENT_SECRET:i6cXM7Zt4X5yrVT7-GI7nGWvU2JLZMCiuPlErN2TAUo}
  redirect-uri: ${ONEPASS_REDIRECT_URI:https://zmyq-ycjf-test.ycfin.net/login/code}
  # 组织信息缓存配置
  group:
    cache:
      ttl: ${ONEPASS_GROUP_CACHE_TTL:3600000} # 缓存有效期（毫秒，默认1小时）
      refresh: ${ONEPASS_GROUP_CACHE_REFRESH:1800000} # 缓存定时刷新间隔（毫秒，默认30分钟）