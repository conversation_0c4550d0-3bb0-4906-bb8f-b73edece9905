# 本地开发环境配置
# 使用方式：java -jar app.jar --spring.profiles.active=local
# 或者在IDE中设置 Active profiles: local

sina:
  api:
    # 启用本地文件模式，用于测试数据采集功能
    use:
      local:
        file: true
    # 本地测试数据文件路径
    local:
      file:
        path: docs/yuqing.txt
    # 默认获取限制（用于分页测试）
    default:
      fetch:
        limit: 10

# 调度配置 - 本地环境禁用定时任务
schedule:
  collector:
    enabled: false
    environment: local
    cron: "0 */5 * * * ?"
    batch-size: 10

# 日志配置
logging:
  level:
    root: INFO
    com.czb.hn.service.collector: DEBUG
    com.czb.hn.service.sina: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 数据库配置（如果需要的话，可以使用H2内存数据库进行测试）
spring:
  datasource:
    # 可以配置为使用H2内存数据库进行本地测试
    # url: jdbc:h2:mem:testdb
    # driver-class-name: org.h2.Driver
    # username: sa
    # password: 
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate


alert:
  processing:
    time-window-hours: 240
